<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.BusinessFlowModuleMapper">
  <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.BusinessFlowModule">
    <!--@mbg.generated-->
    <!--@Table t_business_flow_module-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sort_by" jdbcType="INTEGER" property="sortBy" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="is_effect" jdbcType="INTEGER" property="isEffect" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, sort_by, parent_id, create_by, create_name, create_time, update_by, update_time, 
    update_name, is_effect
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_business_flow_module
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="sort_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sortBy,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.parentId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_business_flow_module
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.name != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sort_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sortBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.sortBy,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.parentId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.parentId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_effect = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEffect != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isEffect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_business_flow_module
    (`name`, sort_by, parent_id, create_by, create_name, create_time, update_by, update_time, 
      update_name, is_effect)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.sortBy,jdbcType=INTEGER}, #{item.parentId,jdbcType=BIGINT}, 
        #{item.createBy,jdbcType=BIGINT}, #{item.createName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateBy,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateName,jdbcType=VARCHAR}, 
        #{item.isEffect,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_business_flow_module
    (`name`, sort_by, parent_id, create_by, create_name, create_time, update_by, update_time, 
      update_name, is_effect)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.sortBy,jdbcType=INTEGER}, #{item.parentId,jdbcType=BIGINT}, 
        #{item.createBy,jdbcType=BIGINT}, #{item.createName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateBy,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateName,jdbcType=VARCHAR}, 
        #{item.isEffect,jdbcType=INTEGER})
    </foreach>
    on duplicate key update 
    name=values(name),
    sort_by=values(sort_by),
    parent_id=values(parent_id),
    create_by=values(create_by),
    create_name=values(create_name),
    create_time=values(create_time),
    update_by=values(update_by),
    update_time=values(update_time),
    update_name=values(update_name),
    is_effect=values(is_effect)
  </insert>
  <insert id="insertOnDuplicateUpdate" keyColumn="id" keyProperty="id" parameterType="com.lanhu.lims.gateway.admin.model.BusinessFlowModule" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_business_flow_module
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      `name`,
      sort_by,
      parent_id,
      create_by,
      create_name,
      create_time,
      update_by,
      update_time,
      update_name,
      is_effect,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{name,jdbcType=VARCHAR},
      #{sortBy,jdbcType=INTEGER},
      #{parentId,jdbcType=BIGINT},
      #{createBy,jdbcType=BIGINT},
      #{createName,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP},
      #{updateName,jdbcType=VARCHAR},
      #{isEffect,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      `name` = #{name,jdbcType=VARCHAR},
      sort_by = #{sortBy,jdbcType=INTEGER},
      parent_id = #{parentId,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_name = #{updateName,jdbcType=VARCHAR},
      is_effect = #{isEffect,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.lanhu.lims.gateway.admin.model.BusinessFlowModule" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_business_flow_module
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="sortBy != null">
        sort_by,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="isEffect != null">
        is_effect,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sortBy != null">
        #{sortBy,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="isEffect != null">
        #{isEffect,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sortBy != null">
        sort_by = #{sortBy,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="isEffect != null">
        is_effect = #{isEffect,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>