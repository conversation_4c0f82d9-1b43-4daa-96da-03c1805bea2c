<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.flow.mapper.WarmFlowMapper">

    <resultMap type="com.lanhu.lims.gateway.admin.flow.vo.resp.FlowTaskVo" id="FlowTaskResult">
        <result property="taskId" column="id"/>
        <result property="nodeName" column="node_name"/>
        <result property="definitionId" column="definition_id"/>
        <result property="instanceId" column="instance_id"/>
        <result property="flowStatus" column="flow_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="businessId" column="business_id"/>
        <result property="flowName" column="flow_name"/>
        <result property="applyUserName" column="nick_name"/>
        <result property="categoryName" column="category_name"/>
        <result property="applyUserId" column="create_by"/>
        <result property="category" column="category"/>
        <result property="taskCreateTime" column="task_create_time"/>
    </resultMap>

    <resultMap type="org.dromara.warm.flow.orm.entity.FlowUser" id="FlowUserResult">
        <result property="id" column="user_id"/>
        <result property="type" column="type"/>
        <result property="processedBy" column="processed_by"/>
        <result property="associated" column="associated"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <resultMap type="org.dromara.warm.flow.orm.entity.FlowHisTask" id="FlowHisTaskResult">
        <result property="id" column="id"/>
        <result property="nodeCode" column="node_code"/>
        <result property="nodeName" column="node_name"/>
        <result property="nodeType" column="node_type"/>
        <result property="targetNodeCode" column="target_node_code"/>
        <result property="targetNodeName" column="target_node_name"/>
        <result property="approver" column="approver"/>
        <result property="collaborator" column="collaborator"/>
        <result property="definitionId" column="definition_id"/>
        <result property="instanceId" column="instance_id"/>
        <result property="taskId" column="task_id"/>
        <result property="cooperateType" column="cooperate_type"/>
        <result property="flowStatus" column="flow_status"/>
        <result property="message" column="message"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="businessId" column="business_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="formPath" column="form_path"/>
        <result property="flowName" column="flow_name"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>



    <select id="toDoPage" resultMap="FlowTaskResult">
        SELECT
        distinct t.id,
            t.node_name,
            t.definition_id,
            t.instance_id,
            t.create_time as task_create_time,
            i.create_time,
            i.update_time,
            t.flow_status,
            d.flow_name,
            i.create_by,
            i.business_id,
            d.category

        FROM flow_task AS t
        LEFT JOIN flow_user uu ON uu.associated = t.id
        Left Join lims.flow_definition d on t.definition_id = d.id
        Left Join lims.flow_instance i on t.instance_id = i.id


        <where>
<!--            流状态为1（审批中），或者9（退回）仍然可以办理-->
            t.node_type = 1 and t.flow_status in (1,9)


            <!--     根据当前用户权限过滤任务       -->
            <if test="params.permissionList != null and params.permissionList.size > 0">
                AND  uu.processed_by in
                <foreach item="permission" collection="params.permissionList" open="(" separator="," close=")">
                    #{permission}
                </foreach>
            </if>


            <!--     根据流程类别过滤任务       -->
            <if test="params.categoryIdList != null and params.categoryIdList.size > 0">
                AND  d.category in
                <foreach item="category" collection="params.categoryIdList" open="(" separator="," close=")">
                    #{category}
                </foreach>
            </if>

        </where>

        <choose>
            <when test="params.orderBy == 1" >
                order by t.create_time asc
            </when>

            <when test="params.orderBy == 2" >
                order by i.create_time desc
            </when>

            <when test="params.orderBy == 3" >
                order by i.create_time asc
            </when>

            <otherwise>
                order by t.create_time desc
            </otherwise>

        </choose>


    </select>





    <select id="donePage" resultMap="FlowTaskResult">
        select
            t.node_name,
            t.definition_id,
            t.instance_id,
            t.task_id as id,
            i.flow_status,
            t.create_time as task_create_time,
            t.update_time,
            i.business_id,
            i.create_by,
            i.create_time,
            i.update_time,
            d.flow_name,
            d.category
        from ( SELECT MAX(id) as id
               FROM flow_his_task
               <where>
                   <if test="approver != null">
                       and approver = #{approver}
                   </if>
<!--                   指定中间节点-->
                   and node_type = 1
               </where>
              GROUP BY instance_id ) tmp
        LEFT JOIN flow_his_task t ON t.id = tmp.id
        LEFT JOIN flow_definition d on t.definition_id = d.id
        LEFT JOIN flow_instance i on t.instance_id = i.id
        <!--     根据流程类别过滤任务       -->
        <if test="params.categoryIdList != null and params.categoryIdList.size > 0">
            AND  d.category in
            <foreach item="category" collection="params.categoryIdList" open="(" separator="," close=")">
                #{category}
            </foreach>
        </if>
        order by t.create_time desc
    </select>


<!--    我的抄送查询   -->
    <select id="copyPage" resultMap="FlowTaskResult">
        SELECT
            c.nick_name AS nick_name,
            b.flow_status,
            b.business_id,
            a.create_time,
            b.node_name,
            b.id as instance_id,
            b.id ,
            d.flow_name,
            d.id as definition_id,
            b.update_time,
            e.category_name as category_name

        FROM
            `flow_user` a
                LEFT JOIN flow_instance b ON a.associated = b.id
                LEFT JOIN t_admin_user c ON b.create_by = c.id
                LEFT JOIN flow_definition d on b.definition_id=d.id
                LEFT JOIN t_business_flow_category e on d.category=e.id
        WHERE
<!--        4 抄送类型-->
            a.type = 4
        <if test="task.permissionList != null and task.permissionList.size > 0">
            AND  a.processed_by in
            <foreach item="permission" collection="task.permissionList" open="(" separator="," close=")">
                #{permission}
            </foreach>
        </if>
        ORDER BY create_time DESC

    </select>



    <select id="idReverseDisplayName" parameterType="long" resultType="com.lanhu.lims.gateway.admin.model.AdminUser">
        select id,user_name,nick_name
        from t_admin_user where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>






    <resultMap type="com.lanhu.lims.gateway.admin.model.AdminUser" id="AdminUserResult">
        <id     property="id"       column="id"      />
        <result property="deptId"       column="dept_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="email"        column="email"        />
        <result property="mobile"       column="mobile"  />
        <result property="icon"         column="icon"         />
        <result property="password"     column="password"     />
        <result property="status"       column="status"       />
        <result property="isEffect"     column="is_effect"     />
        <result property="loginTime"    column="login_time"   />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <association property="deptId"    javaType="com.lanhu.lims.gateway.admin.model.Dept"    resultMap="deptResult" />
        <collection  property="rolesList" ofType="com.lanhu.lims.gateway.admin.model.Role"   javaType="java.util.List"  resultMap="RoleResult" />
    </resultMap>

    <resultMap id="deptResult" type="com.lanhu.lims.gateway.admin.model.Dept">
        <id     property="deptId"    column="dept_id"     />
        <result property="parentId"  column="parent_id"   />
        <result property="deptName"  column="dept_name"   />
        <result property="ancestors" column="ancestors"   />
        <result property="orderNum"  column="order_num"   />
        <result property="leader"    column="leader"      />
        <result property="status"    column="status" />
    </resultMap>



    <resultMap id="RoleResult" type="com.lanhu.lims.gateway.admin.model.Role">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
    </resultMap>





    <select id="selectNotUserIds" parameterType="com.lanhu.lims.gateway.admin.flow.vo.WarmFlowInteractiveTypeVo" resultMap="AdminUserResult">
        SELECT
            u.id,
            u.dept_id,
            u.nick_name,
            u.user_name,
            u.real_name,
            u.password,
            u.email,
            u.icon,
            u.mobile,
            u.status,
            u.update_time,
            u.update_by,
            u.is_effect,
            u.login_time,
            u.create_by,
            u.create_time,
            d.dept_name,
            d.leader
        from t_admin_user u
        left join t_dept d on u.dept_id = d.dept_id
        where u.is_effect = 0
        <if test="warmFlowInteractiveTypeVo.userIds != null and warmFlowInteractiveTypeVo.userIds.size() > 0">
            AND u.id not in
            <foreach collection="warmFlowInteractiveTypeVo.userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="warmFlowInteractiveTypeVo.deptId != null and warmFlowInteractiveTypeVo.deptId != 0">
            AND (u.dept_id = #{warmFlowInteractiveTypeVo.deptId} OR u.dept_id IN ( SELECT t.dept_id FROM t_dept t WHERE find_in_set(#{warmFlowInteractiveTypeVo.deptId}, ancestors) ))
        </if>
        <!-- 数据范围过滤 -->
<!--        ${warmFlowInteractiveTypeVo.params.dataScope}-->
    </select>



    <select id="selectUserIds" parameterType="com.lanhu.lims.gateway.admin.flow.vo.WarmFlowInteractiveTypeVo" resultMap="AdminUserResult">
        select u.id, u.dept_id, u.nick_name, u.user_name, u.email, u.icon, u.mobile, u.status, u.is_effect, u.login_time, u.create_by, u.create_time, d.dept_name, d.leader from t_admin_user u
        left join t_dept d on u.dept_id = d.dept_id
        where u.is_effect = '0'
        <if test="warmFlowInteractiveTypeVo.userIds != null and warmFlowInteractiveTypeVo.userIds.size() > 0">
            AND u.id in
            <foreach collection="warmFlowInteractiveTypeVo.userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="warmFlowInteractiveTypeVo.deptId != null and warmFlowInteractiveTypeVo.deptId != 0">
            AND (u.dept_id = #{warmFlowInteractiveTypeVo.deptId} OR u.dept_id IN ( SELECT t.dept_id FROM t_dept t WHERE find_in_set(#{warmFlowInteractiveTypeVo.deptId}, ancestors) ))
        </if>
        <!-- 数据范围过滤 -->
<!--        ${warmFlowInteractiveTypeVo.params.dataScope}-->
    </select>




    <select id="selectRoleByIds" parameterType="Long" resultMap="RoleResult">
        select r.role_id, r.role_name
        from t_role r
        where r.role_id in
        <foreach collection="list" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>




    <select id="selectDeptByIds" parameterType="Long" resultMap="deptResult">
        select d.dept_id, d.dept_name, d.parent_id, d.ancestors, d.order_num, d.leader,d.status
        from t_dept d
        where d.dept_id in
        <foreach collection="list" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>





    <select id="selectUserByIds" parameterType="Long" resultMap="AdminUserResult">
        select u.id, u.nick_name
        from t_admin_user u
        where u.id in
        <foreach collection="list" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>





    <select id="definitionList" resultType="org.dromara.warm.flow.orm.entity.FlowDefinition">
        select

            id,
            flow_name,
            flow_code,
            category,
            version,
            is_publish,
            activity_status,
            create_time,
            update_time

        from flow_definition

        <where>
            <if test="definition.flowName!= null and definition.flowName!= ''">
                and flow_name like concat('%', #{definition.flowName}, '%')
            </if>
            <if test="definition.category!= null and definition.category!= ''">
                and category = #{definition.category}
            </if>
            <if test="definition.isPublish!= null and definition.isPublish!= ''">
                and is_publish = #{definition.isPublish}
            </if>

            and del_flag = 0
        </where>
        order by create_time desc
    </select>





    <select id="myApplyPage" resultType="com.lanhu.lims.gateway.admin.flow.vo.resp.FlowTaskVo">

        select i.id as instance_id,
            i.create_time as create_time,
            i.update_time as update_time,
            d.id as definition_id,
            i.id as business_id,
            i.flow_status as flow_status,
            d.flow_name as flow_name,
            i.node_name as node_name,
            d.category
        from flow_instance i
        left join flow_definition d on i.definition_id = d.id
        <where>
            i.create_by = #{userId}
            <if test="params.flowStatus!=null">
                and i.flow_status = #{params.flowStatus}
            </if>
            <!--     根据流程类别过滤任务       -->
            <if test="params.categoryIdList != null and params.categoryIdList.size > 0">
                AND  d.category in
                <foreach item="category" collection="params.categoryIdList" open="(" separator="," close=")">
                    #{category}
                </foreach>
            </if>

            and i.del_flag = 0 and d.del_flag = 0
        </where>
    order by i.create_time desc
    </select>


<!--    查询实例列表-->
    <select id="instanceList" resultType="com.lanhu.lims.gateway.admin.flow.vo.resp.InstanceListVO">
        select
            i.id as instance_id,
            d.id as definition_id,
            d.flow_name as instance_name,
            i.node_name as node_name,
            d.flow_code as flow_code,
            d.version as version,
            i.create_time as create_time,
            i.update_time as update_time,
            i.flow_status as flow_status,
            i.create_by as apply_user_id,
            d.category as categoryId

        from flow_instance i
        left join lims.flow_definition d on i.definition_id = d.id

        <where>
            <if test="form.instanceName!=null">
                and d.flow_name like concat('%', #{form.instanceName}, '%')
            </if>
            <if test="form.category!=null">
                and d.category = #{form.category}
            </if>
            <if test="form.flowStatus != null">
                    and i.flow_status = #{form.flowStatus}
            </if>
            <if test="form.nodeName!= null">
                and i.node_name like concat('%', #{form.nodeName}, '%')
            </if>

        </where>


    </select>
</mapper>
