package com.lanhu.lims.gateway.admin.flow.controller;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.pagehelper.PageInfo;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.flow.service.FlowTaskService;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowHisTaskListForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowTaskMyApplyListForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowTodoTaskListPageForm;
import com.lanhu.lims.gateway.admin.flow.vo.resp.FlowTaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**load
 * 流程实例Controller
 *
 * <AUTHOR>
 * @date 2025-04-28
 */

@RestController
@Api(tags = "工作流-任务管理",value = "工作流-任务管理")
@Slf4j
public class FlowTaskController extends BaseController {


    @Resource
    private FlowTaskService flowTaskService;





    /**
     * 分页待办任务列表
     */
    @PostMapping("/flow/v1/task/toDoPage")
    @ApiOperation("工作流实例 待办任务分页查询")
    public PcsResult<PageInfo<FlowTaskVo>> toDoPage(@Validated @RequestBody FlowTodoTaskListPageForm flowTaskForm) {


        return flowTaskService.toDoPage(flowTaskForm);

    }


    /**
     * 分页已办任务列表
     */
    @PostMapping("/flow/v1/task/donePage")
    @ApiOperation("工作流实例 已办任务分页查询")
    @DS("slave_1")
    public PcsResult<PageInfo<FlowTaskVo>> donePage(@Validated @RequestBody FlowHisTaskListForm flowHisTaskForm) {

        return flowTaskService.donePage(flowHisTaskForm);

    }




    /**
     * 我的申请记录分页查询
     */
    @PostMapping("/flow/v1/task/myPage")
    @ApiOperation("工作流任务 我的申请记录分页查询")
    public PcsResult<PageInfo<FlowTaskVo>> myList(@Validated @RequestBody FlowTaskMyApplyListForm form) {


        return flowTaskService.myApplyPage(form);


    }

















//    /**
//     * 分页抄送任务列表
//     */
//    @PostMapping("/flow/v1/task/copyPage")
//    @ApiOperation("工作流实例 抄送任务分页查询")
//    @DS("slave_1")
//    public PcsResult<PageInfo<FlowTaskVo>> copyPage(@Validated @RequestBody FlowTodoTaskListPageForm flowTaskForm) {
//
//
//        return  flowTaskService.copyPage(flowTaskForm);
//
//    }










//    /**
//     * 处理非办理的流程交互类型 对当前任务进行转办，委派，加签，减签等
//     */
//    @PostMapping("/flow/execute/interactiveType")
//    @ApiOperation("工作流实例 转办，委派，加签，减签")
//    public PcsResult interactiveType(@Validated @RequestBody FlowInteractiveTypeForm flowInteractiveTypeForm) {
//
//        return flowTaskService.interactiveType(BeanUtil.copyProperties(flowInteractiveTypeForm, WarmFlowInteractiveTypeVo.class));
//
//    }



//    /**
//     * 交互类型可以选择的用户  待办任务加签，减签，转办等可以选择的用户
//     */
//    @PostMapping("/flow/instance/interactiveTypeUser")
//    @ApiOperation("工作流实例 查询交互类型可以选择的用户")
//    public PcsResult<PageInfo<AdminUser>> interactiveTypeUser(@Validated @RequestBody FlowInteractiveUserForm flowInteractiveUserForm) {
//
//        return flowTaskService.interactiveTypeUser(flowInteractiveUserForm);
//
//
//    }





//    /**
//     * 根据ID反显姓名
//     **/
//    @PostMapping(value = "/flow/instance/idReverseDisplayName")
//    @ApiOperation("工作流实例 反显姓名")
//    public PcsResult<List<AdminUser>> idReverseDisplayName(@Validated @RequestBody  UserIdReverseDisplayNameForm userIdReverseDisplayNameForm) {
//
//        return flowTaskService.idReverseDisplayName(userIdReverseDisplayNameForm.getIds());
//
//    }




//    /**
//     * 根据业务id返回业务详情信息
//     */
//    @PostMapping("/flow/execute/getBusinessDetail")
//    @ApiOperation("工作流实例 根据业务id返回业务详情信息")
//    public PcsResult<String> getBusinessDetail(@Validated @RequestBody FlowBusinessDetailForm flowBusinessDetailForm) {
//
//        return flowTaskService.getBusinessDetail(flowBusinessDetailForm);
//
//    }






}
