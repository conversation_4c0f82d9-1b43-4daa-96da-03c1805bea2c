package com.lanhu.lims.gateway.admin.flow.vo.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 节点出参VO
 * @author: huangzheng
 * @date: 2025/6/18 10:40
 */

@Data
@ApiModel("节点信息出参VO")
public class NodeVO {


    @ApiModelProperty("节点id")
    private Long id;

    @ApiModelProperty("节点类型")
    private Integer nodeType;

    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("节点编码")
    private String nodeCode;

    @ApiModelProperty("节点关联的流程定义id")
    private Long definitionId;







}
