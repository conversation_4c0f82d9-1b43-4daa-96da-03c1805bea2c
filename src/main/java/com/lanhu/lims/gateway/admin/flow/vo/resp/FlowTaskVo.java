package com.lanhu.lims.gateway.admin.flow.vo.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 待办, 已办， 我的申请，抄送任务通用vo
 *
 */
@Data
@ApiModel("待办, 已办， 我的申请，抄送任务通用 出参")
public class FlowTaskVo {

    /**
     * 主键
     */
    @ApiModelProperty("任务ID")
    private Long taskId;


    /**
     * 对应flow_definition表的id
     */
    @ApiModelProperty("流程定义ID")
    private Long definitionId;

    /**
     * 流程实例表id
     */
    @ApiModelProperty("流程实例ID")
    private Long instanceId;

    /**
     * 流程名称
     */
    @ApiModelProperty("流程定义名称ID")
    private String flowName;

    /**
     * 业务id
     */
    @ApiModelProperty("业务ID")
    private String businessId;




//    /**
//     * 计划审批人
//     */
//    @ApiModelProperty("计划审批人")
//    private String approver;






    /**
     * 审批状态
     */
    @ApiModelProperty("流程状态")
    private String flowStatus;


    /**
     * 流程发起人id
     */
    @ApiModelProperty("流程发起人id")
    @JsonIgnore
    private String applyUserId;


    /**
     * 流程发起人
     */
    @ApiModelProperty("流程发起人")
    private String applyUserName;



    /**
     * 流程发起人头像
     */
    @ApiModelProperty("流程发起人头像")
    private String applyUserIcon;




    /**
     * 流程发起时间
     */
    @ApiModelProperty("流程发起时间")
    private String createTime;



    /**
     * 流程更新时间
     */
    @ApiModelProperty("流程更新时间")
    private String updateTime;



    /**
     * 任务创建时间
     */
    @ApiModelProperty("任务创建时间")
    private String taskCreateTime;



    /**
     * 当前节点名称
     */
    @ApiModelProperty("当前节点名称")
    private String nodeName;



    /**
     * 流程分类id
     */
    @JsonIgnore
    private String category;



    /**
     * 流程分类名称
     */
    @ApiModelProperty("流程分类名称")
    private String categoryName;



}
