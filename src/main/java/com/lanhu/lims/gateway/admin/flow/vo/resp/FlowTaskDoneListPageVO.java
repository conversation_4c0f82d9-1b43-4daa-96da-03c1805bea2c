package com.lanhu.lims.gateway.admin.flow.vo.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 已办任务分页查询出参
 * @author: huangzheng
 * @date: 2025/6/18 11:00
 */

@Data
@ApiModel(value = "已办任务分页查询出参")
public class FlowTaskDoneListPageVO {

    /**
     * 任务ID
     */
    @ApiModelProperty("任务ID")
    private Long taskId;



    /**
     * 对应flow_definition表的id
     */
    @ApiModelProperty("流程定义ID")
    private Long definitionId;



    /**
     * 流程实例表id
     */
    @ApiModelProperty("流程实例ID")
    private Long instanceId;



    /**
     * 流程名称
     */
    @ApiModelProperty("流程定义名称")
    private String flowName;



    /**
     * 业务id， 对应FlowBusinessApplication表的id
     */
    @ApiModelProperty("业务ID")
    private String businessId;


    /**
     * 计划审批人
     */
    @ApiModelProperty("当前任务审批人")
    private String approver;

    /**
     * 转办人
     */
    @ApiModelProperty("转办人")
    private String transferredBy;

    /**
     * 委派人
     */
    @ApiModelProperty
    private String delegate;

    /**
     * 审批状态
     */
    @ApiModelProperty("流程状态")
    private String flowStatus;


    /**
     * 流程发起人
     */
    @ApiModelProperty("流程发起姓名")
    private String userRealName;



    /**
     * 流程发起人头像
     */
    @ApiModelProperty("流程发起人头像")
    private String userIcon;




    /**
     * 流程发起时间
     */
    @ApiModelProperty("流程发起时间")
    private String createTime;



    /**
     * 流程更新时间
     */
    @ApiModelProperty("流程更新时间")
    private String updateTime;



    /**
     * 当前任务名称
     */
    @ApiModelProperty("当前任务名称")
    private String nodeName;








}
