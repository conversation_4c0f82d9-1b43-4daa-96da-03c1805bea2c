package com.lanhu.lims.gateway.admin.utils.fill.annotation;

import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/********************************
 * @title DictDataFill
 * @package com.lanhu.lims.gateway.admin.utils.fill.annotation
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/19 16:24
 * @version 0.0.1
 *********************************/
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DictDataFill {
    /**
     * 字典数据类型
     */
    DictDataTypeEnum dictDataType();

    /**
     * 回写字段
     */
    String writeField() default "";
}
