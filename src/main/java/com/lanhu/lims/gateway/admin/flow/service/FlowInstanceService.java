package com.lanhu.lims.gateway.admin.flow.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.flow.mapper.WarmFlowMapper;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowInstanceActiveForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowInstanceApproveRecordForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowInstanceChartForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowInstanceUnActiveForm;
import com.lanhu.lims.gateway.admin.flow.vo.resp.FlowInstanceRecordVo;
import com.lanhu.lims.gateway.admin.flow.vo.resp.InstanceListVO;
import com.lanhu.lims.gateway.admin.mapper.AdminUserMapper;
import com.lanhu.lims.gateway.admin.mapper.BusinessFlowModuleMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.utils.StringUtil;
import com.lanhu.lims.gateway.admin.vo.req.FlowInstanceListForm;
import org.dromara.warm.flow.core.chart.BetweenChart;
import org.dromara.warm.flow.core.chart.FlowChart;
import org.dromara.warm.flow.core.entity.HisTask;
import org.dromara.warm.flow.core.service.ChartService;
import org.dromara.warm.flow.core.service.HisTaskService;
import org.dromara.warm.flow.core.service.InsService;
import org.dromara.warm.flow.core.utils.MapUtil;
import org.dromara.warm.flow.orm.entity.FlowHisTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.awt.*;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 工作流实例服务
 * @author: huangzheng
 * @date: 2025/5/29 14:53
 */

@Service
public class FlowInstanceService {



    @Resource
    private AdminUserMapper adminUserMapper;

    @Resource
    private HisTaskService hisTaskService;

    @Resource
    private ChartService chartService;

    @Resource
    private FlowTaskService flowTaskService;


    @Resource
    private InsService insService;


    @Resource
    private WarmFlowMapper warmFlowMapper;
    @Autowired
    private BusinessFlowModuleMapper businessFlowModuleMapper;


    /**
     * 获取流程实例审批记录
     */
    @DS("slave_1")
    public PcsResult<List<FlowInstanceRecordVo>> getFlowInstanceRecord(FlowInstanceApproveRecordForm flowInstanceApproveRecordForm) {

        // 查询流程实例审批记录   已经办理的任务节点
        List<HisTask> flowHisTasks = hisTaskService.orderById().desc().
                list(new FlowHisTask().setInstanceId(flowInstanceApproveRecordForm.getFlowInstanceId()));



        List<FlowInstanceRecordVo> flowHisTaskList = Lists.newArrayList();

        for (HisTask hisTask : flowHisTasks) {

            FlowInstanceRecordVo flowInstanceRecordVo = BeanUtil.copyProperties(hisTask, FlowInstanceRecordVo.class);

            // 更改时间格式

            flowInstanceRecordVo.setCreateTime(DateUtil.format(hisTask.getCreateTime(),ProjectConstant.DATE_TIME_FORMAT));

            flowInstanceRecordVo.setUpdateTime(DateUtil.format(hisTask.getUpdateTime(),ProjectConstant.DATE_TIME_FORMAT));

            flowHisTaskList.add(flowInstanceRecordVo);

        }



//        // 根据最后一个办理的节点，查询下一个节点信息
//        HisTask lastHisTask = null;
//        if (CollectionUtils.isNotEmpty(flowHisTasks)){
//            lastHisTask = flowHisTasks.get(0);
//        }else{
//            return Result.ok(new ArrayList<>());
//        }
//
//
//        // 下一个节点可能不止一个
//        String targetNodeCodeStr = lastHisTask.getTargetNodeCode();
//
//        // 判断是不是结束节点
//        Node endNode = FlowEngine.nodeService().getEndNode(lastHisTask.getDefinitionId());
//
//
//        // 如果是不是结束节点，查找后续节点
//        if (!targetNodeCodeStr.equals(endNode.getNodeCode())){
//
//            List<String> targetNodeCodes = ListUtil.of(targetNodeCodeStr.split(ProjectConstant.COMMA));
//
//            List<Node> nextNodeCodes = FlowEngine.nodeService().getByNodeCodes(targetNodeCodes, lastHisTask.getDefinitionId());
//
//            if (CollectionUtils.isNotEmpty(nextNodeCodes)){
//                for (Node node : nextNodeCodes) {
//                    FlowInstanceRecordVo flowInstanceRecordVo = new FlowInstanceRecordVo();
//                    flowInstanceRecordVo.setNodeName(node.getNodeName());
//                    flowInstanceRecordVo.setFlowStatus(FlowStatusEnum.APPROVAL.getCode());
//                    flowInstanceRecordVo.setApprover(node.getPermissionFlag());
//                    flowInstanceRecordVo.setNodeType(node.getNodeType());
//                    // 添加到列表头部
//                    flowHisTaskList.add(0, flowInstanceRecordVo);
//                }
//            }
//        }else {
//            // 是结束节点，直接将结束节点加入
//            FlowInstanceRecordVo flowInstanceRecordVo = new FlowInstanceRecordVo();
//            flowInstanceRecordVo.setNodeName(endNode.getNodeName());
//            // 添加到列表头部
//            flowHisTaskList.add(0, flowInstanceRecordVo);
//
//        }

        // 用户名id和用户名
        Set<Long> approverIdList = flowHisTaskList.stream().map(vo -> Long.valueOf(vo.getApprover())).collect(Collectors.toSet());

        Map<Long, AdminUser> userMap = flowTaskService.getUserMap(approverIdList);

        // 回显办理人信息
        for (FlowInstanceRecordVo vo : flowHisTaskList) {

            if (StringUtil.isNotBlank(vo.getApprover())) {
                AdminUser approver = userMap.getOrDefault(Long.valueOf(vo.getApprover()), new AdminUser());
                vo.setApprover(approver.getRealName());
                vo.setApproverIcon(approver.getIcon());
                vo.setApproverDept(approver.getDeptName());
            }


        }


        return Result.ok(flowHisTaskList);
    }





    /**
     * 获取流程实例审批记录
     */
    @DS("slave_1")
    public PcsResult<String> getFlowInstanceChart(FlowInstanceChartForm flowInstanceChartForm) {
        return Result.ok(chartService.chartIns(flowInstanceChartForm.getInstanceId(), (flowChartChain) -> {
            List<FlowChart> flowChartList = flowChartChain.getFlowChartList();
            flowChartList.forEach(flowChart -> {
                if (flowChart instanceof BetweenChart) {
                    BetweenChart betweenChart = (BetweenChart) flowChart;
                    Map<String, Object> extMap = betweenChart.getNodeJson().getExtMap();
                    // 给节点顶部增加文字说明
//                    betweenChart.topText("办理时间: "+ LocalDateTime.now(), Color.red);
                    if (MapUtil.isNotEmpty(extMap)) {
                        for(Map.Entry<String, Object> entry : extMap.entrySet()){
                            // 给节点中追加文字
                            betweenChart.addText(entry.getKey() + ProjectConstant.COLON, Color.red);
                            betweenChart.addText((String) entry.getValue(), Color.red);
                        }
                    }
                }
            });
        }));
    }










    /**
     * 激活实例
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult<Boolean> active(FlowInstanceActiveForm flowInstanceActiveForm) {
        return Result.ok(insService.active(flowInstanceActiveForm.getInstanceId()));
    }



    /**
     * 挂起实例
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult<Boolean> unActive(FlowInstanceUnActiveForm flowInstanceUnActiveForm) {


        return Result.ok(insService.unActive(flowInstanceUnActiveForm.getInstanceId()));

    }



    /**
     * 实例列表
     */
    @DS("slave_1")
    public PcsResult<PageInfo<InstanceListVO>> list(FlowInstanceListForm flowInstanceListForm) {


        PageHelper.startPage(flowInstanceListForm.getPageIndex(), flowInstanceListForm.getPageSize());



        List<InstanceListVO> record = warmFlowMapper.instanceList(flowInstanceListForm);




        // 组装申请人姓名和类别
        if (CollectionUtils.isNotEmpty(record)) {

            // 找出申请id集合
            Set<Long> applyUserIdSet = record.stream().map(InstanceListVO::getApplyUserId).collect(Collectors.toSet());

            Map<Long, AdminUser> userMap = flowTaskService.getUserMap(applyUserIdSet);

            // 找出流程类别id集合
            Map<String, String> categoryMap = flowTaskService.getCategoryMap(null);

            // 组装申请人
            record.forEach(instanceListVO -> instanceListVO.setApplyUser(
                    userMap.getOrDefault(
                            instanceListVO.getApplyUserId(),
                            new AdminUser())
                            .getRealName()));


            // 组装流程类别名称
            record.forEach(instanceListVO -> instanceListVO.setCategoryName(
                    categoryMap.getOrDefault(
                            instanceListVO.getCategoryId().toString(),
                            ProjectConstant.EMPTY)));



        }



        return Result.ok(PageInfo.of(record));




    }
}
