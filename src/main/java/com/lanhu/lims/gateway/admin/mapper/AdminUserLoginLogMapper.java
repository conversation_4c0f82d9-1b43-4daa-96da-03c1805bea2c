package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.AdminUserLoginLog;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AdminUserLoginLogMapper extends BaseMapper<AdminUserLoginLog> {
    int updateBatch(List<AdminUserLoginLog> list);

    int updateBatchSelective(List<AdminUserLoginLog> list);

    int batchInsert(@Param("list") List<AdminUserLoginLog> list);

    int insertOrUpdate(AdminUserLoginLog record);

    int insertOrUpdateSelective(AdminUserLoginLog record);

    AdminUserLoginLog selectByToken(String token);
}