package com.lanhu.lims.gateway.admin.flow.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @description: 工作流定义修改
 * @author: huangzheng
 * @date: 2025/4/30 15:35
 */

@Data
@ApiModel("工作流定义修改表单")
public class FlowDefinitionEditForm {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键",required = true)
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    private Long id;


    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String flowName;

    /**
     * 流程类别
     */
    @ApiModelProperty(value = "流程类别")
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String category;






}
