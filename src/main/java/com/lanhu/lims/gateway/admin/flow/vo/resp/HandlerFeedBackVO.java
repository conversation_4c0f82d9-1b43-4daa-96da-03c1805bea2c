package com.lanhu.lims.gateway.admin.flow.vo.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;


/**
 * @description: 流程设计页面节点办理人回显出参
 * @author: huangzheng
 * @date: 2025/6/19 14:59
 */


@Data
@ApiModel(value = "流程设计页面节点办理人回显出参", description = "流程设计页面节点办理人回显出参")
@AllArgsConstructor
public class HandlerFeedBackVO {

    /**
     * 入库主键，比如怕角色和用户id重复，可拼接为role:id
     */
    @ApiModelProperty(value = "入库主键，比如怕角色和用户id重复，可拼接为role:id")
    private String storageId;



    /**
     * 权限名称，如：管理员、角色管理员、部门管理员等名称
     */
    @ApiModelProperty(value = "权限名称，如：管理员、角色管理员、部门管理员等名称")
    private String handlerName;
}
