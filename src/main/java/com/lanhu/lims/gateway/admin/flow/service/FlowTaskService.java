package com.lanhu.lims.gateway.admin.flow.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.flow.adapter.WarmFlowAdapter;
import com.lanhu.lims.gateway.admin.flow.mapper.WarmFlowMapper;
import com.lanhu.lims.gateway.admin.flow.vo.WarmFlowInteractiveTypeVo;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowHisTaskListForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowInteractiveUserForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowTaskMyApplyListForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowTodoTaskListPageForm;
import com.lanhu.lims.gateway.admin.flow.vo.resp.FlowTaskVo;
import com.lanhu.lims.gateway.admin.mapper.*;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.service.DeptService;
import com.lanhu.lims.gateway.admin.utils.StringUtil;
import com.lanhu.lims.gateway.admin.vo.req.FlowBusinessDetailForm;
import org.dromara.warm.flow.core.entity.User;
import org.dromara.warm.flow.core.enums.CooperateType;
import org.dromara.warm.flow.core.service.*;
import org.dromara.warm.flow.core.utils.StreamUtils;
import org.dromara.warm.flow.orm.entity.FlowTask;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程执行SERVICEIMPL
 *
 * <AUTHOR>
 * @since 2023/5/29 13:09
 */
@Service
public class FlowTaskService {

    @Resource
    private WarmFlowMapper flowMapper;

    @Resource
    private DeptMapper deptMapper;

    @Resource
    private RoleMapper roleMapper;



    @Resource
    private NodeService nodeService;

    @Resource
    private InsService insService;

    @Resource
    private UserService flowUserservice;


    @Resource
    private FlowTaskService flowTaskService;


    @Resource
    private AdminUserMapper adminUserMapper;

    @Resource
    private HisTaskService hisTaskService;



    @Resource
    private ChartService chartService;



    @Resource
    private BusinessFlowApplicationMapper businessFlowApplicationMapper;


    @Resource
    private DeptService deptService;



    @Resource
    private BusinessFlowModuleMapper businessFlowModuleMapper;


    private static final List<WarmFlowAdapter> WARM_FLOW_ADAPTERS = Lists.newArrayList();






    
    
    /**
     * 初始化方法 加载所有的适配器
     */
    @PostConstruct
    public void initMethod() {
        Map<String, WarmFlowAdapter> beanNamesForType = SpringUtil.getBeansOfType(WarmFlowAdapter.class);
        WARM_FLOW_ADAPTERS.addAll(beanNamesForType.values());
    }

    
    
    
    /**
     * 分页查询待办任务
     */
    @DS("slave_1")
    public PcsResult<PageInfo<FlowTaskVo>> toDoPage(FlowTodoTaskListPageForm flowTaskForm) {

        // 解析用户
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);


        // 获取用户工作流办理权限列表
        List<String> permissionList = loginUser.getFlowPermissions();


        flowTaskForm.setPermissionList(permissionList);


        // 获取类别列表
        Map<String,String> categoryMap = flowTaskService.getCategoryMap(flowTaskForm.getFlowCategory());

        if (CollUtil.isNotEmpty(categoryMap)) {
            flowTaskForm.setCategoryIdList(new ArrayList<>(categoryMap.keySet()));
        }






        // 分页查询 获取任务列表
        PageHelper.startPage(flowTaskForm.getPageIndex(), flowTaskForm.getPageSize());
        List<FlowTaskVo> flowTasks = flowMapper.toDoPage(flowTaskForm);


        // 填充申请人信息,和categoryName
        if (CollUtil.isNotEmpty(flowTasks)) {

            Set<Long> applyUserIds = flowTasks.stream().map(flowTaskVo -> Long.valueOf(flowTaskVo.getApplyUserId())
            ).collect(Collectors.toSet());

            Map<Long, AdminUser> adminUserMap = flowTaskService.getUserMap(applyUserIds);


            flowTasks.forEach(flowTaskVo -> {
                    flowTaskVo.setApplyUserName(adminUserMap.get(Long.valueOf(flowTaskVo.getApplyUserId())).getRealName());
                    flowTaskVo.setApplyUserIcon(adminUserMap.get(Long.valueOf(flowTaskVo.getApplyUserId())).getIcon());
                    flowTaskVo.setCategoryName(categoryMap.get(flowTaskVo.getCategory()));

                });

            }













//        // 获取任务id列表
//        List<Long> taskIds = StreamUtils.toList(flowTasks, FlowTaskVo::getTaskId);
//
//        // 查询根据任务ID查询相关的用户信息
//        List<User> userList = flowUserservice.getByAssociateds(taskIds);

//        // 根据处理的任务ID进行分组    key：任务ID   value：处理此任务的用户列表
//        Map<Long, List<User>> map = StreamUtils.groupByKey(userList, User::getAssociated);

//        // 遍历任务列表，为每个任务设置处理人、转交人、委托人信息
//        for (FlowTaskVo taskVo : flowTasks) {
//            if (taskVo!=null) {
//                // 获取当前任务的处理人、转交人、委托人信息
//                List<User> users = map.get(taskVo.getTaskId());
//                if (CollectionUtils.isNotEmpty(users)) {
//                    // 遍历用户列表，设置处理人、转交人、委托人信息
//                    for (User user : users) {
//                        if (UserType.APPROVAL.getKey().equals(user.getType())) {
//                            if (StringUtil.isEmpty(taskVo.getApprover())) {
//                                // 便于后续concat操作，先初始化
//                                taskVo.setApprover(ProjectConstant.EMPTY);
//                            }
//                            // 根据权限id，用户id，部门id等，获取物理名称  用代理类调用getName防止DS注解失效
//                            String name = flowTaskService.getName(user.getProcessedBy());
//                            if (StringUtil.isNotEmpty(name)) {
//                                taskVo.setApprover(taskVo.getApprover().concat(name).concat(ProjectConstant.SEMICOLON));
//                            }
//                        }


                        // 设置转办人，委托人等用户信息
//                        else if (UserType.TRANSFER.getKey().equals(user.getType())) {
//                            if (StringUtil.isEmpty(taskVo.getTransferredBy())) {
//                                taskVo.setTransferredBy(ProjectConstant.EMPTY);
//                            }
//                            String name = flowTaskService.getName(user.getProcessedBy());
//                            if (StringUtil.isNotEmpty(name)) {
//                                taskVo.setTransferredBy(taskVo.getTransferredBy().concat(name).concat(ProjectConstant.SEMICOLON));
//                            }
//                        } else if (UserType.DEPUTE.getKey().equals(user.getType())) {
//                            if (StringUtil.isEmpty(taskVo.getDelegate())) {
//                                taskVo.setDelegate(ProjectConstant.EMPTY);
//                            }
//                            String name = flowTaskService.getName(user.getProcessedBy());
//                            if (StringUtil.isNotEmpty(name)) {
//                                taskVo.setDelegate(taskVo.getDelegate().concat(name).concat(ProjectConstant.SEMICOLON));
//                            }
//                        }
//                    }
//                }
//            }
//        }


        PageInfo<FlowTaskVo> flowTaskVoPageInfo = new PageInfo<>(flowTasks);

        return Result.ok(flowTaskVoPageInfo);


    }


    
    /**
     * 分页查询已办任务
     
     */
    @DS("slave_1")
    public PcsResult<PageInfo<FlowTaskVo>> donePage(FlowHisTaskListForm flowHisTaskForm) {


        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        Map<String, String> categoryMap = flowTaskService.getCategoryMap(flowHisTaskForm.getFlowCategory());

        flowHisTaskForm.setCategoryIdList(new ArrayList<>(categoryMap.keySet()));

        PageHelper.startPage(flowHisTaskForm.getPageIndex(), flowHisTaskForm.getPageSize());
        List<FlowTaskVo> flowTaskVos = flowMapper.donePage(flowHisTaskForm, loginUser.getUserId());



        // 填充用户信息和类别信息
        if (CollUtil.isNotEmpty(flowTaskVos)) {
            Set<Long> applyUserList = flowTaskVos.stream().map(flowTaskVo -> Long.valueOf(flowTaskVo.getApplyUserId())
            ).collect(Collectors.toSet());

            Map<Long, AdminUser> adminUserMap = flowTaskService.getUserMap(applyUserList);


            flowTaskVos.forEach(flowTaskVo -> {
                flowTaskVo.setApplyUserName(adminUserMap.get(Long.valueOf(flowTaskVo.getApplyUserId())).getRealName());
                flowTaskVo.setApplyUserIcon(adminUserMap.get(Long.valueOf(flowTaskVo.getApplyUserId())).getIcon());
                flowTaskVo.setCategoryName(categoryMap.get(flowTaskVo.getCategory()));
            });

        }


        return Result.ok(new PageInfo<>(flowTaskVos));


    }

    
    

    
    /**
     * 分页查询抄送任务
     */
    @DS("slave_1")
    public PcsResult<PageInfo<FlowTaskVo>> copyPage(FlowTodoTaskListPageForm flowTaskListForm) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        // 获取用户工作流办理权限列表
        List<String> permissionList = loginUser.getFlowPermissions();
//        flowTaskListForm.setPermissionList(permissionList);

        PageHelper.startPage(flowTaskListForm.getPageIndex(), flowTaskListForm.getPageSize());
        FlowTask flowTask = BeanUtil.copyProperties(flowTaskListForm, FlowTask.class);
        List<FlowTaskVo> flowTaskVos = flowMapper.copyPage(flowTask);

        return Result.ok(new PageInfo<>(flowTaskVos));

    }



    
    /**
     * 根据ID反显姓名
     *
     **/
    @DS("slave_1")
    public PcsResult<List<AdminUser>> idReverseDisplayName(List<Long> ids) {
        if (ids.isEmpty()) {
            return null;
        }
        return Result.ok(flowMapper.idReverseDisplayName(ids));
    }



    

    /**
     * 查询不包含输入的所有用户
     */
    @DS("slave_1")
    public List<AdminUser> selectNotUserList(WarmFlowInteractiveTypeVo warmFlowInteractiveTypeVo) {
        return flowMapper.selectNotUserIds(warmFlowInteractiveTypeVo);
    }





    /**
     * 查询包含输入的所有用户
     */
    @DS("slave_1")
    public List<AdminUser> selectUserList(WarmFlowInteractiveTypeVo warmFlowInteractiveTypeVo) {

        return flowMapper.selectUserIds(warmFlowInteractiveTypeVo);
        
    }




    /**
     * 查询我的申请列表
     *
     */
    @DS("slave_1")
    public  PcsResult<PageInfo<FlowTaskVo>> myApplyPage(FlowTaskMyApplyListForm form) {

//        Long userId = SecurityUtils.getUserId();
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);
        Long userId = loginUser.getUserId();

        Map<String, String> categoryMap = flowTaskService.getCategoryMap(form.getFlowCategory());

        form.setCategoryIdList(new ArrayList<>(categoryMap.keySet()));


        PageHelper.startPage(form.getPageIndex(), form.getPageSize());
        List<FlowTaskVo> flowTaskVos = flowMapper.myApplyPage(form, userId);

        // 填充用户信息和类别信息
        if (CollUtil.isNotEmpty(flowTaskVos)) {

            flowTaskVos.forEach(flowTaskVo -> {
                flowTaskVo.setApplyUserName(loginUser.getRealName());
                flowTaskVo.setApplyUserIcon(loginUser.getIcon());
                flowTaskVo.setCategoryName(categoryMap.get(flowTaskVo.getCategory()));
            });

        }


        return Result.ok(new PageInfo<>(flowTaskVos));

    }



    /**
     *  根据ID反显拼接后的姓名,部门名或角色名
     */
    @DS("slave_1")
    public String getName(String id) {
        // TODO 根据具体展示的形式 ，修改硬编码
        List<AdminUser> adminUserList = adminUserMapper.selectList(null);
        if (CollUtil.isEmpty(adminUserList)) {
            return "";
        }
        Map<Long, String> userMap = StreamUtils.toMap(
                adminUserList,
                AdminUser::getId,
                AdminUser::getRealName);
        Map<Long, String> deptMap = StreamUtils.toMap(deptMapper.selectList(null)
                , Dept::getDeptId, Dept::getDeptName);
        Map<Long, String> roleMap = StreamUtils.toMap(roleMapper.selectList(null)
                , Role::getRoleId, Role::getRoleName);
        if (StrUtil.isNotEmpty(id)) {
            if (id.contains("user:")) {
                String name = userMap.get(Long.valueOf(id.replace("user:", "")));
                if (StringUtil.isNotEmpty(name)) {
                    return "用户:" + name;
                }
            } else if (id.contains("dept:")) {
                String name = deptMap.get(Long.valueOf(id.replace("dept:", "")));
                if (StringUtil.isNotEmpty(name)) {
                    return "部门:" + name;
                }
            } else if (id.contains("role")) {
                String name = roleMap.get(Long.valueOf(id.replace("role:", "")));
                if (StringUtil.isNotEmpty(name)) {
                    return "角色:" + name;
                }
            } else {
                try {
                    long parseLong = Long.parseLong(id);
                    String name = userMap.get(parseLong);
                    if (StringUtil.isNotEmpty(name)) {
                        return "用户:" + name;
                    }
                } catch (NumberFormatException e) {
                    return id;
                }

            }
        }
        return "";
    }










    /**
     * 查询对应非办理类型（加签，减签）等可以选择的用户
     */
    @DS("slave_1")
    public PcsResult<PageInfo<AdminUser>> interactiveTypeUser(FlowInteractiveUserForm flowInteractiveUserForm) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER,LoginUser.class);

        WarmFlowInteractiveTypeVo warmFlowInteractiveTypeVo = BeanUtil.copyProperties(flowInteractiveUserForm, WarmFlowInteractiveTypeVo.class);

        Integer operatorType = warmFlowInteractiveTypeVo.getOperatorType();

        List<AdminUser> list;

        Long taskId = warmFlowInteractiveTypeVo.getTaskId();
        List<User> users = flowUserservice.listByAssociatedAndTypes(taskId);

        if (!Objects.equals(CooperateType.REDUCTION_SIGNATURE.getKey(), operatorType)) {

            List<String> userIds = StreamUtils.toList(users, User::getProcessedBy);
            warmFlowInteractiveTypeVo.setUserIds(userIds);
            PageHelper.startPage(flowInteractiveUserForm.getPageIndex(), flowInteractiveUserForm.getPageSize());
            list = flowTaskService.selectNotUserList(warmFlowInteractiveTypeVo);

        } else {

            List<String> userIds = StreamUtils.toList(users, User::getProcessedBy);
            warmFlowInteractiveTypeVo.setUserIds(userIds);
            PageHelper.startPage(flowInteractiveUserForm.getPageIndex(), flowInteractiveUserForm.getPageSize());
            list = flowTaskService.selectUserList(warmFlowInteractiveTypeVo);
            list = StreamUtils.filter(list, user -> !Objects.equals(loginUser.getUserId(), user.getId()));

        }

        return Result.ok(new PageInfo<>(list));


    }






    /**
     * 获取业务信息详情
     */
    @DS("slave_1")
    public PcsResult<String> getBusinessDetail(FlowBusinessDetailForm flowBusinessDetailForm) {

        // todo
//        BusinessFlowApplication businessFlowApplication = businessFlowApplicationMapper.selectById(flowBusinessDetailForm.getBusinessId());
//        if (businessFlowApplication == null){
//            return Result.error(PcsResultCode.BUSINESS_NOT_EXISTS);
//        }
////        String busInfo = businessFlowApplication.getBusInfo();


        return Result.ok(null);
    }




    /**
     * 进行加签，减签，委派等操作
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult interactiveType(WarmFlowInteractiveTypeVo warmFlowInteractiveTypeVo) {
        Integer operatorType = warmFlowInteractiveTypeVo.getOperatorType();
        for (WarmFlowAdapter warmFlowAdapter : WARM_FLOW_ADAPTERS) {
            if (warmFlowAdapter.isAdapter(operatorType)) {
                if (warmFlowAdapter.adapter(warmFlowInteractiveTypeVo))
                {
                    return Result.ok();
                }
            }
        }
        return Result.error();
    }






    /**
     * 根据类别id获取类别列表包含其子类别
     * @return categoryMap   key category的id value category的值
     */
    @DS("slave_1")
    public Map<String, String> getCategoryMap(Long categoryId) {
        // 查询出所有的模块类别信息
        LambdaQueryWrapper<BusinessFlowModule> businessFlowModuleLambdaQueryWrapper = Wrappers.lambdaQuery(BusinessFlowModule.class);
        businessFlowModuleLambdaQueryWrapper.eq(BusinessFlowModule::getIsEffect, IsEffectEnum.NORMAL.getCode());
        businessFlowModuleLambdaQueryWrapper.select(BusinessFlowModule::getId, BusinessFlowModule::getName, BusinessFlowModule::getParentId);
        List<BusinessFlowModule> allBusinessFlowModuleList = businessFlowModuleMapper.selectList(businessFlowModuleLambdaQueryWrapper);


        Map<String, BusinessFlowModule> businessFlowModuleMap = StreamUtils.toMap
                (allBusinessFlowModuleList,
                        businessFlowModule->businessFlowModule.getId().toString(),
                        businessFlowModule -> businessFlowModule);


        // 填充categoryList
        if (categoryId != null) {

            List<BusinessFlowModule> categoryList = Lists.newArrayList();
            BusinessFlowModule businessFlowModule = businessFlowModuleMap.get(categoryId.toString());
            if (businessFlowModule != null) {
                // 是模块
                if (businessFlowModule.getParentId() == 0L) {

                    List<BusinessFlowModule> businessFlowModuleList = allBusinessFlowModuleList.stream()
                            .filter(module -> module.getParentId().equals(businessFlowModule.getId()))
                            .collect(Collectors.toList());

                    categoryList.addAll(businessFlowModuleList);

                } else {
                    categoryList.add(businessFlowModule);
                }

            }
            return StreamUtils.toMap(categoryList,
                    module-> module.getId().toString(),
                    BusinessFlowModule::getName);
        }
        // 为空 返回所有的信息
            return  businessFlowModuleMap.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().getName()
        ));

    }


    /**
     * 根据用户id集合获取用户姓名头像信息
     */
    @DS("slave_1")
    public Map<Long,AdminUser> getUserMap(Collection<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return null;
        }
        LambdaQueryWrapper<AdminUser> adminUserLambdaQueryWrapper = Wrappers.lambdaQuery(AdminUser.class);
        adminUserLambdaQueryWrapper.select(AdminUser::getId, AdminUser::getRealName, AdminUser::getIcon,AdminUser::getDeptId);
        adminUserLambdaQueryWrapper.in(AdminUser::getId, userIds);
        adminUserLambdaQueryWrapper.eq(AdminUser::getIsEffect, IsEffectEnum.NORMAL.getCode());

        List<AdminUser> adminUserList = adminUserMapper.selectList(adminUserLambdaQueryWrapper);
        if (CollUtil.isEmpty(adminUserList)) {
            return null;
        }
        // 查询部门表
        List<Dept> deptList = deptService.list();

        // 填充部门名称并返回
        return StreamUtils.toMap(adminUserList,AdminUser::getId, adminUser->{
            adminUser.setDeptName(deptService.deptNameTranslation(deptList,adminUser.getDeptId()));
            return adminUser;
        });

    }


















}
