package com.lanhu.lims.gateway.admin.auth.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lanhu.lims.gateway.admin.auth.config.TokenConfig;
import com.lanhu.lims.gateway.admin.auth.exception.TokenAuthException;
import com.lanhu.lims.gateway.admin.auth.jwt.Jwt;
import com.lanhu.lims.gateway.admin.auth.jwt.JwtResult;
import com.lanhu.lims.gateway.admin.auth.utils.SecurityUtils;
import com.lanhu.lims.gateway.admin.auth.utils.StringUtils;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.core.RedisKeyConstant;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.enums.LanguageEnum;
import com.lanhu.lims.gateway.admin.enums.MenuLevelEnum;
import com.lanhu.lims.gateway.admin.enums.TokenStateEnum;
import com.lanhu.lims.gateway.admin.file.service.IFileService;
import com.lanhu.lims.gateway.admin.mapper.*;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.service.UserRoleService;
import com.lanhu.lims.gateway.admin.utils.ServletUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * token验证处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class TokenService
{

    @Autowired
    private TokenConfig tokenConfig;


    @Autowired
    private AdminUserMapper adminUserMapper;


    @Autowired
    private MenuMapper menuMapper;


    @Autowired
    private AdminUserLoginLogMapper adminUserLoginLogMapper;

    @Autowired
    private UserRoleService userRoleService;


    @Resource
    private AdminUserRoleMapper adminUserRoleMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private DeptMapper deptMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IFileService fileService;

    /**
     * 创建令牌
     */
    public String  createToken(Long userId)
    {


        return Jwt.createToken(userId,tokenConfig);


    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser()
    {
        return getLoginUser(ServletUtils.getRequest());
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request)
    {
        // 获取请求携带的令牌
        String token = SecurityUtils.getToken(request);
        return getLoginUser(token);
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    @DS("slave_1")
    private LoginUser getLoginUser(String token)
    {
        LoginUser user = null;

        AdminUser adminUser = null;

        if (StringUtils.isNotEmpty(token))
        {
            Long userId = verifyToken(token);

            adminUser = adminUserMapper.selectById(userId);

            if(adminUser == null){
                throw  new BusinessException(PcsResultCode.PLA_USER_NOT_EXISTS);
            }

            user =  assemblyLoginUser(adminUser,false);

            user.setToken(token);

            return user;
        }
        return user;
    }



    /**
     * 验证令牌有效期，相差不足120分钟，自动刷新缓存
     *
     * @param token
     */
    @DS("slave_1")
    public Long verifyToken(String token)
    {

        if(StringUtils.isNotEmpty(token)) {
            // 核对token
            JwtResult jwt = Jwt.checkToken(token,tokenConfig);
            // 如果token过期
            if (TokenStateEnum.EXPIRED.getState().equals(jwt.getState())) {
                throw new TokenAuthException(PcsResultCode.TOKEN_TIMEOUT);
            }else if(TokenStateEnum.INVALID.getState().equals(jwt.getState())) {
                throw new TokenAuthException(PcsResultCode.INVALID_TOKEN);
            }



          //  AdminUserLoginLog adminUserLoginLog = adminUserLoginLogMapper.selectByToken(SecureUtil.md5(token));

            //token续签
            boolean validateAndRenew = validateAndRenew(token);

            if(!validateAndRenew){
                throw new TokenAuthException(PcsResultCode.INVALID_TOKEN);
            }



            return jwt.getMemberId();
        }else {
            throw new TokenAuthException(PcsResultCode.NOT_TOKEN);
        }
    }



    /**
     * 组装用户登录信息
     * @param adminUser 商家用户信息
     * @return 用户登录信息
     */
    @DS("slave_1")
    public LoginUser assemblyLoginUser(AdminUser adminUser, boolean isCreateToken) {


        LoginUser loginUser = new LoginUser();

        boolean isSuperAdmin = userRoleService.isSuperAdmin(adminUser);

        List<Menu> menuList = Lists.newArrayList();

        if(isSuperAdmin){
            menuList = menuMapper.selectMenuList();
        }else {
            //加载权限菜单列表形成树形结构
            menuList = menuMapper.selectMenuListByUserId(adminUser.getId());
        }


        //菜单名称翻译
        if(CollectionUtil.isNotEmpty(menuList)){
            for (Menu menu : menuList) {
                menuNameTranslate(menu);
            }
        }

        //菜单树形结构
        if(CollectionUtil.isNotEmpty(menuList)){


            List<Menu> menuList1 = menuList.stream().filter(m->(m.getLevel() == MenuLevelEnum.LEVEL_1.getCode() && m.getParentId().equals(0L))).sorted(Comparator.comparing(Menu::getOrd)).collect(Collectors.toList());

            //一级菜单
            if(CollectionUtil.isNotEmpty(menuList1)){


                for (Menu menu1 : menuList1) {


                    List<Menu> menuList2 = menuList.stream().filter(m->(m.getLevel() == MenuLevelEnum.LEVEL_2.getCode() && m.getParentId().equals(menu1.getId()))).sorted(Comparator.comparing(Menu::getOrd)).collect(Collectors.toList());


                    if(CollectionUtil.isNotEmpty(menuList2)){


                        for (Menu menu2 : menuList2) {


                            List<Menu> menuList3 = menuList.stream().filter(m->(m.getLevel() == MenuLevelEnum.LEVEL_3.getCode() && m.getParentId().equals(menu2.getId()))).sorted(Comparator.comparing(Menu::getOrd)).collect(Collectors.toList());


                            if(CollectionUtil.isNotEmpty(menuList3)){

                                menu2.setChildren(menuList3);

                            }else {
                                menu2.setChildren(Lists.newArrayList());
                            }

                        }



                        menu1.setChildren(menuList2);

                    }else {
                        menu1.setChildren(Lists.newArrayList());

                    }

                }
                adminUser.setMenuList(menuList1);
            }

        }






        if(isCreateToken){
            loginUser.setToken(createToken(adminUser.getId()));
        }


        loginUser.setUserId(adminUser.getId());
        loginUser.setUsername(adminUser.getUserName());
        loginUser.setSysUser(adminUser);
        loginUser.setPermissions(permissionList(menuList));
        loginUser.setRealName(adminUser.getRealName());
        loginUser.setNickName(adminUser.getNickName());
        loginUser.setIcon(fileService.getUrl(adminUser.getIcon()));
        loginUser.setWorkStatus(adminUser.getWorkStatus());


        // 设置工作流任务办理权限
        List<String> flowTaskPermissions = Lists.newArrayList();


        //工作流指定到人
        flowTaskPermissions.add(adminUser.getId().toString());

        //工作流指定到部门
        if (StringUtils.isNotBlank(adminUser.getDeptId())){
            String[] deptIds = adminUser.getDeptId().split(ProjectConstant.COMMA);
            for (int i = 0; i < deptIds.length; i++) {
                flowTaskPermissions.add("dept:"+ deptIds[i]);
            }

            if(deptIds.length > 0){

              List<Dept> deptList =   deptMapper.selectBatchIds(Lists.newArrayList(deptIds));

              if(CollectionUtil.isNotEmpty(deptList)){
                  //多个部门以逗号隔开
                  loginUser.setDeptName( String.join(ProjectConstant.COMMA,deptList.stream().map(Dept::getDeptName).collect(Collectors.toList())));
              }
            }


        }

        LambdaQueryWrapper<AdminUserRole> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(AdminUserRole::getAdminUserId,adminUser.getId());

        List<AdminUserRole> adminUserRoles = adminUserRoleMapper.selectList(queryWrapper);


        //工作流指定到角色
        if (CollectionUtil.isNotEmpty(adminUserRoles)){
            List<String> roleIds = adminUserRoles.stream().
                    map(adminUserRole ->
                 "role:"+adminUserRole.getRoleId()
            ).collect(Collectors.toList());
            flowTaskPermissions.addAll(roleIds.stream().map(String::valueOf).collect(Collectors.toList()));

            List<Role> roleList = roleMapper.selectBatchIds(adminUserRoles.stream().map(AdminUserRole::getRoleId).collect(Collectors.toList()));

            if(CollectionUtil.isNotEmpty(roleList)){
                //多个角色以逗号隔开
                loginUser.setRoleName( String.join(ProjectConstant.COMMA,roleList.stream().map(Role::getRoleName).collect(Collectors.toList())));
            }
        }

        loginUser.setFlowPermissions(flowTaskPermissions);


        return loginUser;
    }


    /**
     * @description: 菜单名称翻译
     * @param: [menu]
     * @return: void
     * @author: liuyi
     * @date: 5:31 下午 2023/3/15
     */
    public void menuNameTranslate(Menu menu){

        try{
            if(menu != null && StringUtils.isNotBlank(menu.getMenuNameTranslate())){

                String[] languageList = menu.getMenuNameTranslate().split(ProjectConstant.COMMA);


                Integer index = Integer.valueOf(LanguageEnum.convert(LocaleContextHolder.getLocale().getLanguage().concat(ProjectConstant.UNDER_LINE).concat(LocaleContextHolder.getLocale().getCountry())).getMsg());


                if(languageList.length > index){
                    String translateName =  languageList[index];
                    if(org.apache.commons.lang3.StringUtils.isNotBlank(translateName)){
                        menu.setMenuName(translateName);
                    }
                }


            }
        }catch (Exception e){

        }


    }

    @DS("master_1")
    public void delLoginUser(String token){

        if(!StringUtils.isEmpty(token)) {


//            AdminUserLoginLog adminUserLoginLog = adminUserLoginLogMapper.selectByToken(SecureUtil.md5(token));
//
//            if(adminUserLoginLog != null){
//                AdminUserLoginLog update = AdminUserLoginLog.builder()
//                        .updateTime(DateUtil.date())
//                        .isEffect(IsEffectEnum.DELETE.getCode())
//                        .id(adminUserLoginLog.getId())
//                        .build();
//
//                adminUserLoginLogMapper.updateById(update);
//            }

            delToken(token);


        }
    }



    private List<String> permissionList(  List<Menu> menuList){

        List<String>  permissionList = Lists.newArrayList();

        if(CollectionUtil.isNotEmpty(menuList)){
            permissionList = menuList.stream().filter(x-> StringUtils.isNotBlank(x.getPerms())).map(Menu::getPerms).distinct().collect(Collectors.toList());
        }

        return permissionList;
    }

    // 生成token并存入Redis
    public void saveToken(String token,String userId) {

        //登录token redis key
        String loginTokenKey = MessageFormat.format(RedisKeyConstant.LOGIN_TOKEN_KEY, SecureUtil.md5(token));

        redisTemplate.opsForValue().set(loginTokenKey,userId, tokenConfig.getExpire(), TimeUnit.SECONDS);
    }

    // 验证token是否有效，并自动续签
    public boolean validateAndRenew(String token) {

        String loginTokenKey = MessageFormat.format(RedisKeyConstant.LOGIN_TOKEN_KEY, SecureUtil.md5(token));

        // 判断token是否存在
        if (redisTemplate.opsForValue().get(loginTokenKey) != null) {
            // 获取剩余过期时间
            long ttl = redisTemplate.getExpire(loginTokenKey,TimeUnit.SECONDS);

            if (ttl > 0 && ttl < tokenConfig.getRenew()) {
                // 续签，延长过期时间
                redisTemplate.expire(loginTokenKey, tokenConfig.getExpire(),TimeUnit.SECONDS);
            }
            return true;
        }
        return false;
    }

    public void delToken(String token) {

        //登录token redis key
        String loginTokenKey = MessageFormat.format(RedisKeyConstant.LOGIN_TOKEN_KEY, SecureUtil.md5(token));

        redisTemplate.delete(loginTokenKey);
    }


}