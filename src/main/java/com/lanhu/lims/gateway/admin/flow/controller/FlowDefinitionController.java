package com.lanhu.lims.gateway.admin.flow.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.flow.service.FlowDefService;
import com.lanhu.lims.gateway.admin.flow.service.FlowTaskService;
import com.lanhu.lims.gateway.admin.flow.vo.req.*;
import com.lanhu.lims.gateway.admin.flow.vo.resp.HandlerFeedBackVO;
import com.lanhu.lims.gateway.admin.flow.vo.resp.NodeVO;
import com.lanhu.lims.gateway.admin.mapper.DeptMapper;
import com.lanhu.lims.gateway.admin.mapper.RoleMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.BusinessFlowModule;
import com.lanhu.lims.gateway.admin.model.Dept;
import com.lanhu.lims.gateway.admin.model.Role;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.dromara.warm.flow.core.FlowEngine;
import org.dromara.warm.flow.core.dto.DefJson;
import org.dromara.warm.flow.core.entity.Definition;
import org.dromara.warm.flow.core.exception.FlowException;
import org.dromara.warm.flow.orm.entity.FlowDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 工作流 流程定义Controller
 *
 * <AUTHOR>
 * @date 2025-04-28
 */

@RestController
@Api(tags = "工作流-流程定义管理",value = "工作流-流程定义管理")
@Slf4j
public class FlowDefinitionController extends BaseController {


    @Resource
    private FlowDefService flowDefService;

    @Resource
    private FlowTaskService flowTaskService;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private DeptMapper deptMapper;


    /**
     * 分页查询流程定义列表
     */
    @PostMapping("/flow/definition/v1/list")
    @ApiOperation("工作流定义 分页查询")
    public PcsResult<PageInfo<FlowDefinition>> list(@Validated @RequestBody FlowDefinitionListForm form) {
        return flowDefService.list(form);
    }



    /**
     * 获取流程定义详细信息
     */
    @PostMapping(value = "/flow/definition/v1/detail")
    @ApiOperation("工作流定义 详情查询")
    public PcsResult<Definition> getInfo(@Validated @RequestBody  FlowDefinitionDetailForm form) {
        return flowDefService.getInfo(form);
    }


    /**
     * 新增流程定义
     */
//    @RequiresPermissions("flow:definition:add")
    @Log(title = "流程定义", businessType = LogBusinessType.INSERT)
    @PostMapping("/flow/definition/v1/add")
    @ApiOperation("工作流定义 新增")
    public PcsResult add(@Validated @RequestBody FlowDefinitionAddForm flowDefinitionForm) {

        return flowDefService.add(flowDefinitionForm);

    }



    /**
     * 发布流程定义
     */
//    @RequiresPermissions("flow:definition:publish")
    @Log(title = "流程定义", businessType = LogBusinessType.PUBLISH_FLOW_DEFINITION)
    @PostMapping("/flow/definition/v1/publish")
    @ApiOperation("工作流定义 发布")
    public PcsResult publish(@Validated @RequestBody FlowDefinitionPublishForm definitionPublishForm) {

        return flowDefService.publish(definitionPublishForm);

    }



    /**
     * 取消发布流程定义
     */
//    @RequiresPermissions("flow:definition:unpublish")
    @Log(title = "流程定义", businessType = LogBusinessType.UNPUBLISH_FLOW_DEFINITION)
    @PostMapping("/flow/definition/v1/unPublish")
    @ApiOperation("工作流定义 取消发布")
    public PcsResult unPublish(@Validated @RequestBody FlowDefinitionUnPublishForm definitionUnPublishForm) {

        return flowDefService.unPublish(definitionUnPublishForm);

    }


    /**
     * 修改流程定义
     */
//    @RequiresPermissions("flow:definition:edit")
    @Log(title = "流程定义", businessType = LogBusinessType.UPDATE)
    @PostMapping("/flow/definition/v1/edit")
    @ApiOperation("工作流定义 修改")
    public PcsResult edit(@Validated @RequestBody FlowDefinitionEditForm form) {

        return flowDefService.edit(form);

    }


    /**
     * 删除流程定义
     */
//    @RequiresPermissions("flow:definition:del")
    @Log(title = "流程定义", businessType = LogBusinessType.LOGICDELETE)
    @PostMapping("/flow/definition/v1/del")
    @ApiOperation("工作流定义 批量删除")
    public PcsResult remove(@Validated @RequestBody FlowDefinitionBatchDelForm form) {
        return flowDefService.removeDef(form);
    }


    /**
     * 复制流程定义
     */
//    @RequiresPermissions("flow:definition:copy")
    @Log(title = "流程定义", businessType = LogBusinessType.INSERT)
    @PostMapping("/flow/definition/v1/copyDef")
    @ApiOperation("工作流定义 复制")
    public PcsResult copyDef(@Validated @RequestBody FlowDefinitionCopyForm form) {
        return flowDefService.copyDef(form);
    }


//    /**
//     * 导出工作流定义
//     */
//    @Log(title = "流程定义", businessType = LogBusinessType.IMPORT)
//    @RequiresPermissions("flow:definition:import")
//    @PostMapping("/flow/definition/import")
//    @ApiOperation("导入工作流定义")
//    public PcsResult importDefinition(MultipartFile file) throws IOException {
//
//        return flowDefService.importIs(file);
//
//    }


//    @Log(title = "流程定义", businessType = LogBusinessType.EXPORT)
//    @RequiresPermissions("flow:definition:export")
//    @PostMapping("/flow/definition/export")
//    @ApiOperation("导出工作流定义")
//    public ResponseEntity<byte[]> exportDefinition(@Validated @RequestBody FlowDefinitionExportForm form) {
//        // 要导出的字符串
//        String content = flowDefService.exportJson(form);
//
//        // 设置响应头
//        HttpHeaders headers = new HttpHeaders();
//        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=exported_string.txt");
//
//        // 返回响应
//        return ResponseEntity.ok()
//                .headers(headers)
//                .contentType(MediaType.TEXT_PLAIN)
//                .body(content.getBytes(StandardCharsets.UTF_8));
//    }


    /**
     * 查询流程图
     *
     */
    @PostMapping("/flow/definition/v1/chartDef")
    @ApiOperation("工作流定义 查看流程定义图")
    @DS("slave_1")
    public PcsResult<String> chartDef(@Validated @RequestBody FlowDefinitionChartForm flowDefinitionChartForm) {

        return flowDefService.chartDef(flowDefinitionChartForm);

    }




    /**
     * 激活流程
     */
//    @RequiresPermissions("flow:definition:active")
    @PostMapping("/flow/definition/v1/active")
    @Log(title = "流程定义", businessType = LogBusinessType.ENABLE)
    @ApiOperation("工作流定义 激活流程")
    public PcsResult active(@Validated @RequestBody FlowDefinitionActiveForm flowDefinitionActiveForm) {

        return flowDefService.active(flowDefinitionActiveForm);

    }


    /**
     * 挂起流程
     */
//    @RequiresPermissions("flow:definition:unActive")
    @Log(title = "流程定义", businessType = LogBusinessType.DISABLE)
    @PostMapping("/flow/definition/v1/unActive")
    @ApiOperation("工作流定义 挂起流程")
    public PcsResult unActive(@Validated @RequestBody FlowDefinitionUnActiveForm flowDefinitionUnActiveForm) {

        return flowDefService.unActive(flowDefinitionUnActiveForm);

    }









    /**
     * 查询工作流定义中的任意节点列表
     *
     */
    @PostMapping("/flow/definition/v1/anyNodeList")
    @ApiOperation("工作流实例 任意节点查询")
    public PcsResult<List<NodeVO>> anyNodeList(@Validated @RequestBody FlowAnyNodeListForm flowAnyNodeListForm) {


        return flowDefService.anyNodeList(flowAnyNodeListForm);

    }




    /**
     * 保存流程设计json字符串
     *
     */
    @ApiOperation("流程设计")
    @PostMapping("/flow/definition/v1/design")
    public PcsResult<Void> definitionDesign(@RequestBody DefJson defJson) throws Exception {


        return flowDefService.definitionDesign(defJson);


    }





    /**
     * 查询流程的类型列表 树形
     *
     */
    @PostMapping("/flow/category/v1/list")
    @ApiOperation("查询流程的类型列表 树形")
    public PcsResult<List<BusinessFlowModule>> categoryTreeList(){


        return flowDefService.categoryTreeList();


    }


    @PostMapping("/flow/definition/v1/Def")
    @ApiOperation("回显流程定义图")
    public PcsResult<DefJson> queryDef(@Validated @RequestBody FlowDefinitionDefForm form) {

        try {
            return Result.ok(FlowEngine.defService().queryDesign(form.getId()));
        } catch (Exception e) {
            throw new FlowException();
        }

    }





    /**
     * 办理人权限名称回显
     * @return HandlerSelectVo
     */
    @PostMapping("/flow/definition/v1/handler-feedback")
    @ApiOperation("办理人权限名称回显")
    public PcsResult<List<HandlerFeedBackVO>> handlerFeedback(@Validated @RequestBody FlowNodeHandlerFeedbackForm form) {

        final String rolePrefix = "role:";
        final String deptPrefix = "dept:";

        List<Long> userIdList =  Lists.newArrayList();
        List<Long> roleIdList =  Lists.newArrayList();
        List<Long> deptIdList =  Lists.newArrayList();

        List<HandlerFeedBackVO> HandlerFeedBackVOList = Lists.newArrayList();


        List<String> storageIdList = form.getStorageIds();
        if (CollUtil.isNotEmpty(storageIdList)){
            storageIdList.forEach(
                    storageId -> {
                        if (storageId.startsWith(rolePrefix)){
                            roleIdList.add(Long.valueOf(storageId.substring(5)));
                        }else if (storageId.startsWith(deptPrefix)){
                            deptIdList.add(Long.valueOf(storageId.substring(5)));
                        }else {
                            userIdList.add(Long.valueOf(storageId));
                        }
                    }
            );
        }


        // 查询用户信息
        if (CollUtil.isNotEmpty(userIdList)){
            Map<Long, AdminUser> userMap = flowTaskService.getUserMap(userIdList);
            if(userMap != null){
                userMap.forEach((k,v)->{
                    HandlerFeedBackVOList.add(new HandlerFeedBackVO(k.toString(),v.getRealName()));
                });
            }


        }

        // 查询角色信息
        if (CollUtil.isNotEmpty(roleIdList)){
            List<Role> roleList = roleMapper.selectBatchIds(roleIdList);

            if (CollUtil.isNotEmpty(roleList)){
                roleList.forEach(role -> {
                    HandlerFeedBackVOList.add(new HandlerFeedBackVO(rolePrefix+role.getRoleId().toString(),role.getRoleName()));
                });
            }
        }



        // 查询部门信息
            if (CollUtil.isNotEmpty(deptIdList)){
                List<Dept> deptList = deptMapper.selectBatchIds(deptIdList);
                if (CollUtil.isNotEmpty(deptList)){
                    deptList.forEach(dept -> {
                        HandlerFeedBackVOList.add(new HandlerFeedBackVO(deptPrefix+dept.getDeptId().toString(),dept.getDeptName()));
                    });

            }




    }

        return Result.ok(HandlerFeedBackVOList);
}
}
