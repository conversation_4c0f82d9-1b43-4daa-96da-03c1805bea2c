package com.lanhu.lims.gateway.admin.flow.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lanhu.lims.gateway.admin.core.PageForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 工作流已办列表
 * @author: huangzheng
 * @date: 2025/4/29 11:16
 */

@Data
@ApiModel(value = "工作流已办列表")
public class FlowTaskMyApplyListForm extends PageForm {



    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private Integer flowStatus;



    /**
     * 流程类别
     */
    @ApiModelProperty("流程类别  可以传模块也可以传具体的类别，不传默认查询所有")
    private Long flowCategory;




    /**
     * 类别列表
     */
    @JsonIgnore
    private List<String> categoryIdList;





}
