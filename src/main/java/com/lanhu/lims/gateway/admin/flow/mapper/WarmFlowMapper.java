package com.lanhu.lims.gateway.admin.flow.mapper;


import com.lanhu.lims.gateway.admin.flow.vo.WarmFlowInteractiveTypeVo;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowDefinitionListForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowHisTaskListForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowTodoTaskListPageForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowTaskMyApplyListForm;
import com.lanhu.lims.gateway.admin.flow.vo.resp.FlowTaskVo;
import com.lanhu.lims.gateway.admin.flow.vo.resp.InstanceListVO;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.Dept;
import com.lanhu.lims.gateway.admin.model.Role;
import com.lanhu.lims.gateway.admin.vo.req.FlowInstanceListForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.warm.flow.orm.entity.FlowDefinition;
import org.dromara.warm.flow.orm.entity.FlowTask;

import java.util.List;

/**
 * warm-flow工作流Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Mapper
public interface WarmFlowMapper {
    /**
     * 分页查询待办任务
     *
     * @param task 条件实体
     */
    List<FlowTaskVo> toDoPage(@Param("params") FlowTodoTaskListPageForm task);

    /**
     * 获取最新的已办任务
     *
     * @return
     */
    List<FlowTaskVo> donePage(@Param("params") FlowHisTaskListForm form, @Param("approver") Long userId);

    /**
     * 分页获取抄送任务
     * @param flowTask
     * @return
     */
    List<FlowTaskVo> copyPage(@Param("task") FlowTask flowTask);

    /**
     * 根据ID反显姓名
     *
     * @param ids 需要反显姓名的用户ID
     * @return {@link List<AdminUser>}
     * <AUTHOR>
     * @date 2024/8/21 17:11
     **/
    List<AdminUser> idReverseDisplayName(@Param("ids") List<Long> ids);

    /**
     * 查询不为输入的所有用户
     *
     * @param warmFlowInteractiveTypeVo 输入用户编号集合
     * @return 用户列表
     */
    List<AdminUser> selectNotUserIds(@Param("warmFlowInteractiveTypeVo") WarmFlowInteractiveTypeVo warmFlowInteractiveTypeVo);

    /**
     * 查询包含输入的所有用户
     *
     * @param warmFlowInteractiveTypeVo 输入用户编号集合
     * @return 用户列表
     */
    List<AdminUser> selectUserIds(@Param("warmFlowInteractiveTypeVo")WarmFlowInteractiveTypeVo warmFlowInteractiveTypeVo);

    /**
     * 通过角色ID集合查询角色
     *
     * @param roleIds 角色ID集合
     * @return 角色对象信息
     */
    public List<Role> selectRoleByIds(List<Long> roleIds);

    /**
     * 根据部门ID集合查询信息
     *
     * @param deptIds 部门ID集合
     * @return 部门信息
     */
    public List<Dept> selectDeptByIds(List<Long> deptIds);

    /**
     * 通过用户ID集合查询用户
     *
     * @param userIds 用户ID集合
     * @return 用户对象信息
     */
    List<AdminUser> selectUserByIds(List<Long> userIds);




    /**
     * 通过用户ID集合查询用户
     *
     * @param form 分页查询参数
     * @return 流程定义列表
     */
    List<FlowDefinition> definitionList(@Param("definition") FlowDefinitionListForm form);




    /**
     * 查询用户的申请业务列表
     *
     * @param form 分页查询参数
     * @return 流程任务VO列表
     */
    List<FlowTaskVo> myApplyPage(@Param("params") FlowTaskMyApplyListForm form, @Param("userId") Long userId);







    /**
     * 通过用户ID集合查询用户
     *
     * @param form 分页查询参数
     * @return 流程定义列表
     */
    List<InstanceListVO> instanceList(@Param("form") FlowInstanceListForm form);
}
