package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.DeptStatusEnum;
import com.lanhu.lims.gateway.admin.enums.EnableEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.AdminUserMapper;
import com.lanhu.lims.gateway.admin.mapper.DeptMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.Dept;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.utils.StringUtil;
import com.lanhu.lims.gateway.admin.vo.req.DeptAddForm;
import com.lanhu.lims.gateway.admin.vo.req.DeptEditForm;
import com.lanhu.lims.gateway.admin.vo.req.DeptTreeListForm;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 部门管理服务
 * @author: huangzheng
 * @date: 2025/4/29 9:51
 */

@Service
public class DeptService {


    @Resource
    private DeptMapper deptMapper;



    @Resource
    private AdminUserMapper adminUserMapper;

    /**
     * 新增部门信息
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult add(DeptAddForm deptAddForm) {

        Dept dept = BeanUtil.copyProperties(deptAddForm, Dept.class);

        dept.setLeader(ProjectConstant.EMPTY);
        dept.setEmail(deptAddForm.getEmail());
        dept.setPhone(deptAddForm.getPhone());
        dept.setStatus(DeptStatusEnum.NORMAL.getCode());

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        dept.setCreateBy(loginUser.getUserId());
        dept.setCreateTime(DateUtil.date());

        dept.setUpdateBy(loginUser.getUserId());

        dept.setUpdateTime(DateUtil.date());
        deptMapper.insert(dept);
        return Result.ok();

    }

    /**
     * 根据ID删除部门 逻辑删除
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult del(Long deptId) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);


        LambdaQueryWrapper<Dept> wrapper = Wrappers.lambdaQuery();

        wrapper.eq(Dept::getParentId,deptId);
        wrapper.eq(Dept::getIsEffect,IsEffectEnum.NORMAL.getCode());
        wrapper.eq(Dept::getStatus,DeptStatusEnum.NORMAL.getCode());

        Long count = deptMapper.selectCount(wrapper);

        // 若当前部门存在子部门，则不允许删除
        if (count > 0) {
            return Result.error(PcsResultCode.DEPT_HAS_CHILDREN);
        }

        Dept dept = new Dept();
        dept.setDeptId(deptId);

        // 逻辑删除
        dept.setIsEffect(IsEffectEnum.DELETE.getCode());
        dept.setUpdateBy(loginUser.getUserId());
        dept.setUpdateTime(DateUtil.date());

        deptMapper.updateById(dept);
        return Result.ok();
    }





    /**
     * 编辑部门信息
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult edit(DeptEditForm deptEditForm) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);

        Dept dept = BeanUtil.copyProperties(deptEditForm, Dept.class);

        dept.setLeader(deptEditForm.getLeaderName());
        dept.setEmail(deptEditForm.getEmail());
        dept.setPhone(deptEditForm.getPhone());

        dept.setUpdateBy(loginUser.getUserId());
        dept.setUpdateTime(DateUtil.date());

        deptMapper.updateById(dept);
        return Result.ok();
    }


    /**
     * 查看部门详情
     */
    @DS("slave_1")
    public PcsResult<Dept> detail(Long deptId) {

        Dept dept = deptMapper.selectById(deptId);

        if (dept == null) {
            return Result.error(PcsResultCode.DEPT_NOT_EXISTS);
        }


        //如果该部门不是一级部门
        if(dept.getParentId() != null && !dept.getParentId().equals(0L)){

            Dept parentDept = deptMapper.selectById(dept.getParentId());

            dept.setParentDeptName(parentDept.getDeptName());
        }


        return Result.ok(dept);
    }


    @DS("slave_1")
    public PcsResult<List<Dept>> treeList(DeptTreeListForm deptForm) {

        LambdaQueryWrapper<Dept> deptWrapper = Wrappers.lambdaQuery();
        if(deptForm.getStatus() != null ){
            deptWrapper.eq(Dept::getStatus,deptForm.getStatus());
        }
        deptWrapper.eq(Dept::getIsEffect,IsEffectEnum.NORMAL.getCode());

        deptWrapper.orderByAsc(Dept::getDeptName);

        List<Dept> allDepts = deptMapper.selectList(deptWrapper);

        List<Dept> rootDepts = new ArrayList<>();

        Map<Long, Dept> deptMap = new HashMap<>();

        List<String> deptIdList = Lists.newArrayList();

        if(deptForm.getUserId() != null){
          AdminUser adminUser =   adminUserMapper.selectById(deptForm.getUserId());

          if(adminUser != null && StringUtil.isNotBlank(adminUser.getDeptId())){
              deptIdList = CollectionUtil.newArrayList(adminUser.getDeptId().split(ProjectConstant.COMMA));
           }
        }


        // 先将所有部门存入映射中
        for (Dept dept : allDepts) {
            //如果当前部门被选中
            if(CollectionUtil.isNotEmpty(deptIdList) && deptIdList.contains(String.valueOf(dept.getDeptId()))){
                dept.setChecked(true);

            }
            deptMap.put(dept.getDeptId(), dept);
        }




        // 构建树形结构
        for (Dept dept : allDepts) {
            Long parentId = dept.getParentId();
            if (parentId == null || parentId.equals(0L)) {
                // 根部门
                rootDepts.add(dept);
            } else {
                // 非根部门，找到父部门并添加到父部门的子列表中
                Dept parent = deptMap.get(parentId);
                if (parent != null) {
                    if (CollectionUtil.isEmpty(parent.getChildren())) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(dept);
                }
            }
        }

        return Result.ok(rootDepts);
    }



    @DS("master_1")
    @LhTransaction
    public PcsResult disable(Long deptId) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);


        LambdaQueryWrapper<Dept> wrapper = Wrappers.lambdaQuery();

        wrapper.eq(Dept::getParentId,deptId);
        wrapper.eq(Dept::getIsEffect,IsEffectEnum.NORMAL.getCode());
        wrapper.eq(Dept::getStatus,DeptStatusEnum.NORMAL.getCode());


        Long count = deptMapper.selectCount(wrapper);

        // 若当前部门存在子部门，则不允许禁用
        if (count > 0) {
            return Result.error(PcsResultCode.DEPT_HAS_CHILDREN);
        }

        Dept dept = new Dept();
        dept.setDeptId(deptId);
        // 禁用
        dept.setStatus(DeptStatusEnum.DISABLE.getCode());
        dept.setUpdateBy(loginUser.getUserId());
        dept.setUpdateTime(DateUtil.date());

        deptMapper.updateById(dept);
        return Result.ok();

    }


    @DS("master_1")
    @LhTransaction
    public PcsResult enable(Long deptId) {

        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);


        Dept dept = new Dept();
        dept.setDeptId(deptId);

        // 禁用
        dept.setStatus(DeptStatusEnum.NORMAL.getCode());
        dept.setUpdateBy(loginUser.getUserId());
        dept.setUpdateTime(DateUtil.date());

        deptMapper.updateById(dept);
        return Result.ok();

    }

    /**
     * 部门列表
     *
     * @return 部门列表
     */
    @DS("slave_1")
    public List<Dept> list() {
        LambdaQueryWrapper<Dept> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(Dept::getIsEffect, IsEffectEnum.NORMAL.getCode());
        lambdaQuery.eq(Dept::getStatus, EnableEnum.ENABLE.getCode());
        return deptMapper.selectList(lambdaQuery);
    }

    /**
    * @description: 部门名称翻译
    * @param: [deptList, deptIds]
    * @return: java.lang.String
    * @author: liuyi
    * @date: 18:52 2025/6/18 
    */
    public String deptNameTranslation(List<Dept>  deptList,String deptIds){

        if(StringUtils.isBlank(deptIds)){
            return ProjectConstant.EMPTY;
        }

        Map<String,Dept> deptMap = Maps.newHashMap();

        if(CollectionUtil.isNotEmpty(deptList)){
            deptMap = deptList.stream().collect(
                    Collectors.toMap(x->String.valueOf(x.getDeptId()), x->x));
        }

        List<String> deptNameList = Lists.newArrayList();
        //部门
        if(StringUtils.isNotBlank(deptIds)){

            String[] deptArray = deptIds.split(ProjectConstant.COMMA);

            for (int i = 0; i < deptArray.length; i++) {
                if(deptMap.get(deptArray[i]) != null){
                    deptNameList.add(deptMap.get(deptArray[i]).getDeptName());
                }
            }
        }

        if(CollectionUtil.isNotEmpty(deptNameList)){
            return  String.join(ProjectConstant.COMMA,deptNameList);
        }

        return ProjectConstant.EMPTY;

    }
}
