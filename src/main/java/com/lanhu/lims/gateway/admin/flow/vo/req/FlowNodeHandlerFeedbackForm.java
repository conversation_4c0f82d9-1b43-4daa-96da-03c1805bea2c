package com.lanhu.lims.gateway.admin.flow.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 流程节点办理人回显入参
 * @author: huangzheng
 * @date: 2025/6/19 15:03
 */
@Data
@ApiModel(value = "流程节点办理人回显入参")
public class FlowNodeHandlerFeedbackForm {

    /**
     * 入库主键集合，比如怕角色和用户id重复，可拼接为role:id
     */
    @ApiModelProperty(value = "入库主键集合，比如怕角色和用户id重复，可拼接为role:id")
    private List<String> storageIds;




}
