package com.lanhu.lims.gateway.admin.flow.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.flow.mapper.WarmFlowMapper;
import com.lanhu.lims.gateway.admin.flow.vo.req.*;
import com.lanhu.lims.gateway.admin.flow.vo.resp.NodeVO;
import com.lanhu.lims.gateway.admin.mapper.BusinessFlowModuleMapper;
import com.lanhu.lims.gateway.admin.model.BusinessFlowModule;
import org.dromara.warm.flow.core.FlowEngine;
import org.dromara.warm.flow.core.dto.DefJson;
import org.dromara.warm.flow.core.entity.Definition;
import org.dromara.warm.flow.core.entity.Node;
import org.dromara.warm.flow.core.service.ChartService;
import org.dromara.warm.flow.core.service.DefService;
import org.dromara.warm.flow.core.service.InsService;
import org.dromara.warm.flow.core.service.NodeService;
import org.dromara.warm.flow.orm.entity.FlowDefinition;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * 流程定义serviceImpl
 *
 * <AUTHOR>
 * @since 2023/5/29 13:09
 */
@Service
public class FlowDefService {

    @Resource
    private DefService defService;


    @Resource
    private WarmFlowMapper flowMapper;


    @Resource
    private ChartService chartService;


    @Resource
    private NodeService nodeService;



    @Resource
    private InsService insService;



    @Resource
    private BusinessFlowModuleMapper businessFlowModuleMapper;


    /**
     * 分页查询流程定义
     */

    @DS("slave_1")
    public PcsResult<PageInfo<FlowDefinition>> list(FlowDefinitionListForm form) {


        PageHelper.startPage(form.getPageIndex(), form.getPageSize());

        List<FlowDefinition> definitions = flowMapper.definitionList(form);

        return Result.ok(new PageInfo<>(definitions));

    }


    /**
     * 获取流程定义详情
     */
    @DS("slave_1")
    public PcsResult<Definition> getInfo(FlowDefinitionDetailForm form) {
        return Result.ok(defService.getById(form.getId()));
    }





    /**
     * 新增流程定义
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult add(FlowDefinitionAddForm flowDefinitionForm) {

        String flowCode = UUID.randomUUID().toString();
        FlowDefinition flowDefinition = BeanUtil.copyProperties(flowDefinitionForm, FlowDefinition.class);
        flowDefinition.setFlowCode(flowCode);
        defService.saveAndInitNode(flowDefinition);

        return Result.ok();
    }



    /**
     * 发布流程
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult publish(FlowDefinitionPublishForm form) {
         defService.publish(form.getId());
         return Result.ok();
    }





    /**
     * 取消发布
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult unPublish(FlowDefinitionUnPublishForm definitionUnPublishForm) {
        defService.unPublish(definitionUnPublishForm.getId());
        return Result.ok();
    }





    /**
     * 编辑流程定义
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult edit(FlowDefinitionEditForm form) {
        defService.updateById(BeanUtil.copyProperties(form, FlowDefinition.class));
        return Result.ok();
    }



    /**
     * 删除流程定义
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult removeDef(FlowDefinitionBatchDelForm form) {


        // removeDef 会判断是否有
        defService.removeDef(form.getIds());
        return Result.ok();
    }



    /**
     * 复制流程定义，用以发布新版本
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult copyDef(FlowDefinitionCopyForm form) {
        defService.copyDef(form.getId());
        return Result.ok();
    }




    /**
     * 导出流程定义
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult importIs(MultipartFile file) throws IOException {

        defService.importIs(file.getInputStream());

        return Result.ok();
    }





    /**
     * 导出流程定义
     */
    @DS("slave_1")
    public String exportJson(FlowDefinitionExportForm form) {
        return  defService.exportJson(form.getId());
    }





    /**
     * 获取流程定义图
     */
    @DS("slave_1")
    public PcsResult<String> chartDef(FlowDefinitionChartForm flowDefinitionChartForm) {
        return Result.ok(chartService.chartDef(flowDefinitionChartForm.getId()));
    }






    /**
     * 激活流程定义
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult active(FlowDefinitionActiveForm flowDefinitionActiveForm) {
        return Result.ok(defService.active(flowDefinitionActiveForm.getId()));

    }




    /**
     * 禁用流程定义
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult unActive(FlowDefinitionUnActiveForm flowDefinitionUnActiveForm) {
        defService.unActive(flowDefinitionUnActiveForm.getId());
        return Result.ok();
    }








    /**
     * 查询流程中的任意节点
     */
    @DS("slave_1")
    public PcsResult<List<NodeVO>> anyNodeList(FlowAnyNodeListForm flowAnyNodeListForm) {


        // 获取该实例的开始节点
        Node startNode = nodeService.getStartNode(flowAnyNodeListForm.getDefinitionId());

        if (startNode == null) {
            return Result.ok(new ArrayList<>());
        }

        // 获取开始节点的后续节点
        List<Node> nodeList = nodeService.suffixNodeList(startNode.getId());

        List<NodeVO> nodeVOList = BeanUtil.copyToList(nodeList, NodeVO.class);


        return Result.ok(nodeVOList);

    }




    /**
     * 保存流程定义 流程设计
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult<Void> definitionDesign(DefJson defJson) {

        try {
            FlowEngine.defService().saveDef(defJson);

        } catch (Exception e) {

            return Result.error(PcsResultCode.FLOW_EXCEPTION);
        }

        return Result.ok();
    }




    /**
     * 树形查询流程分类
     */
    @DS("slave_1")
    public PcsResult<List<BusinessFlowModule>> categoryTreeList() {
        LambdaQueryWrapper<BusinessFlowModule> businessFlowModuleLambdaQueryWrapper = Wrappers.lambdaQuery();

        businessFlowModuleLambdaQueryWrapper.eq(BusinessFlowModule::getIsEffect, IsEffectEnum.NORMAL.getCode());

        List<BusinessFlowModule> businessFlowModuleList = businessFlowModuleMapper.selectList(businessFlowModuleLambdaQueryWrapper);



        List<BusinessFlowModule> moduleList = new ArrayList<>();

        Map<Long, BusinessFlowModule> businessFlowModuleMap = new HashMap<>();


        // 先将所有部门存入映射中
        for (BusinessFlowModule businessFlowModule : businessFlowModuleList ) {
            businessFlowModuleMap.put(businessFlowModule.getId(), businessFlowModule);
        }

        // 构建树形结构
        for (BusinessFlowModule businessFlowModule : businessFlowModuleList ) {
            Long parentId = businessFlowModule.getParentId();
            if (parentId == null || parentId.equals(0L)) {
                // 根部门
                moduleList.add(businessFlowModule);
            } else {
                // 非根部门，找到父部门并添加到父部门的子列表中
                BusinessFlowModule parent = businessFlowModuleMap.get(parentId);
                if (parent != null) {
                    if (CollectionUtil.isEmpty(parent.getSubCategoryList())) {
                        parent.setSubCategoryList(new ArrayList<>());
                    }
                    parent.getSubCategoryList().add(businessFlowModule);
                }
            }
        }


        return Result.ok(moduleList);




    }
}
