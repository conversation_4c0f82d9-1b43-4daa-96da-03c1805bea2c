package com.lanhu.lims.gateway.admin.flow.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lanhu.lims.gateway.admin.core.PageForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 待办任务分页查询
 * @author: huangzheng
 * @date: 2025/4/29 10:16
 */
@Data
@ApiModel("待办任务分页查询")
public class FlowTodoTaskListPageForm extends PageForm {




    /**
     * 流程类别
     */
    @ApiModelProperty("流程类别  可以传模块也可以传具体的类别，不传默认查询所有")
    private Long flowCategory;




    /**
     * 排序规则
     */
    @ApiModelProperty("排序规则 0:最新到达时间优先, 1: 最久等待优先 ,2: 创建时间由近到远 ,3: 创建时间由远到近 ，不传默认规则0")
    private Integer orderBy;



    /**
     * 权限列表
     */
    @JsonIgnore
    private List<String> permissionList;


    /**
     * 类别列表
     */
    @JsonIgnore
    private List<String> categoryIdList;



    //    /**
//     * 创建开始时间
//     */
//    @ApiModelProperty("创建开始时间,格式:yyyy-MM-dd HH:mm:ss")
//    private String createStartTime;
//
//    /**
//     * 创建结束时间
//     */
//    @ApiModelProperty("创建结束时间,格式:yyyy-MM-dd HH:mm:ss")
//    private String createEndTime;



}
