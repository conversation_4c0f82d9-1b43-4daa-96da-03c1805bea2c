package com.lanhu.lims.gateway.admin.flow.vo.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 实例审批记录出参
 * @author: huangzheng
 * @date: 2025/5/16 16:24
 */
@Data
@ApiModel(value = "实例审批记录出参")
public class FlowInstanceRecordVo {



    /**
     * 任务开始时间
     */
    @ApiModelProperty("任务开始时间")
    private String createTime;

    /**
     * 审批完成时间
     */
    @ApiModelProperty("审批完成时间")
    private String updateTime;



    /**
     * 协作方式(1审批 2转办 3委派 4会签 5票签 6加签 7减签)
     */
    @ApiModelProperty("协作方式(1审批 2转办 3委派 4会签 5票签 6加签 7减签)")
    private Integer cooperateType;



    /**
     * 节点名称
     */
    @ApiModelProperty("节点名称")
    private String nodeName;


    /**
     * 节点类型
     */
    @ApiModelProperty("节点类型")
    private Integer nodeType;



    /**
     * 审批者
     */
    @ApiModelProperty("审批者")
    private String approver;


    /**
     * 审批者头像
     */
    @ApiModelProperty("申请人头像")
    private String approverIcon;



    /**
     * 审批者部门
     */
    @ApiModelProperty("申请人部门")
    private String approverDept;



    /**
     * 协作人(只有转办、会签、票签、委派)
     */
    @ApiModelProperty("协作人(只有转办、会签、票签、委派)")
    private String collaborator;



    /**
     * 跳转类型（PASS通过 REJECT退回 NONE无动作）
     */
    @ApiModelProperty("跳转类型（PASS通过 REJECT退回 NONE无动作）")
    private String skipType;



    /**
     * 流程状态（0待提交 1审批中 2审批通过 4终止 5作废 6撤销 8已完成 9已退回 10失效 11拿回）
     */
    @ApiModelProperty("流程状态（0待提交 1审批中 2审批通过 4终止 5作废 6撤销 8已完成 9已退回 10失效 11拿回）")
    private Integer flowStatus;


    /**
     * 审批意见
     */
    @ApiModelProperty("审批意见")
    private String message;







}
