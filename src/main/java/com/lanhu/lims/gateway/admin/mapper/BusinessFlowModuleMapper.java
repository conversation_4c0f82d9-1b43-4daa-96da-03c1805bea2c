package com.lanhu.lims.gateway.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanhu.lims.gateway.admin.model.BusinessFlowModule;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/6/19 15:29
 */
@Mapper
public interface BusinessFlowModuleMapper extends BaseMapper<BusinessFlowModule> {
    int updateBatch(@Param("list") List<BusinessFlowModule> list);

    int updateBatchSelective(@Param("list") List<BusinessFlowModule> list);

    int batchInsert(@Param("list") List<BusinessFlowModule> list);

    int batchInsertOrUpdate(@Param("list") List<BusinessFlowModule> list);

    int insertOnDuplicateUpdate(BusinessFlowModule record);

    int insertOnDuplicateUpdateSelective(BusinessFlowModule record);
}