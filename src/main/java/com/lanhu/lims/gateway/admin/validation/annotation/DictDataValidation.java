package com.lanhu.lims.gateway.admin.validation.annotation;

import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/********************************
 * @title DictDataValidation
 * @package com.lanhu.lims.gateway.admin.validation.annotation
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/19 16:36
 * @version 0.0.1
 *********************************/
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DictDataValidation {
    /**
     * 字典数据类型
     */
    DictDataTypeEnum dictDataType();

    /**
     * 是否必须启用
     */
    boolean isMustEnable() default true;

    /**
     * 回写字段
     */
    String writeBackField() default "";
}
