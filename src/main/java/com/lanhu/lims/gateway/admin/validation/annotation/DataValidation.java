package com.lanhu.lims.gateway.admin.validation.annotation;

import com.lanhu.lims.gateway.admin.validation.enums.DataTypeEnum;
import com.lanhu.lims.gateway.admin.validation.validator.DataValidationValidator;

import javax.validation.Constraint;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/********************************
 * @title DataValidation
 * @package com.lanhu.lims.gateway.admin.validation.annotation
 * @description 通用数据校验注解，支持条件必填
 *
 * <AUTHOR>
 * @date 2025/6/19 16:38
 * @version 0.0.1
 *********************************/
@Target({ElementType.FIELD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DataValidationValidator.class)
public @interface DataValidation {
    /**
     * 数据类型
     */
    DataTypeEnum dataType();

    /**
     * 数据回写目标字段名
     */
    String writeBackField() default "";

    /**
     * 是否必须启用状态
     */
    boolean mustEnabled() default true;
}
