package com.lanhu.lims.gateway.admin.validation;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.lanhu.lims.gateway.admin.validation.annotation.DataValidation;
import com.lanhu.lims.gateway.admin.validation.annotation.DictDataValidation;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

/********************************
 * @title ValidationUtil
 * @package com.lanhu.lims.gateway.admin.validation
 * @description 验证工具类，获取指定注解并进行判定逻辑处理和回写
 *
 * <AUTHOR>
 * @date 2025/6/19 16:40
 * @version 0.0.1
 *********************************/
@Slf4j
public class ValidationUtil {

    /**
     * 处理对象中所有 DataValidation 注解字段
     * 获取注解参数，进行判定逻辑，验证通过则回写到指定字段
     *
     * @param obj 目标对象
     */
    public static void processDataValidation(Object obj) {
        if (obj == null) {
            return;
        }
        Class<?> clazz = obj.getClass();
        Field[] fields = ReflectUtil.getFields(clazz);

        for (Field field : fields) {
            DataValidation annotation = field.getAnnotation(DataValidation.class);
            if (annotation != null) {
                processDataValidationField(obj, field, annotation);
            }
        }
    }

    /**
     * 处理对象中所有 DictDataValidation 注解字段
     * 获取注解参数，进行判定逻辑，验证通过则回写到指定字段
     *
     * @param obj 目标对象
     */
    public static void processDictDataValidation(Object obj) {
        if (obj == null) {
            return;
        }
        Class<?> clazz = obj.getClass();
        Field[] fields = ReflectUtil.getFields(clazz);

        for (Field field : fields) {
            DictDataValidation annotation = field.getAnnotation(DictDataValidation.class);
            if (annotation != null) {
                processDictDataValidationField(obj, field, annotation);
            }
        }
    }

    /**
     * 处理对象中所有验证注解字段（DataValidation 和 DictDataValidation）
     *
     * @param obj 目标对象
     */
    public static void processAllValidation(Object obj) {
        processDataValidation(obj);
        processDictDataValidation(obj);
    }

    /**
     * 处理单个 DataValidation 注解字段
     *
     * @param obj        目标对象
     * @param field      字段
     * @param annotation 注解
     */
    private static void processDataValidationField(Object obj, Field field, DataValidation annotation) {
        try {
            field.setAccessible(true);
            Object fieldValue = field.get(obj);

            // 获取注解参数
            String writeBackField = annotation.writeBackField();
            boolean mustEnabled = annotation.mustEnabled();

            // 进行判定逻辑（这里可以根据具体业务需求扩展）
            boolean validationPassed = performDataValidationLogic(fieldValue, annotation);

            // 如果验证通过且有回写字段，则进行回写
            if (validationPassed && StrUtil.isNotBlank(writeBackField)) {
                Object writeBackValue = getDataValidationWriteBackValue(fieldValue, annotation);
                setFieldValue(obj, writeBackField, writeBackValue);
                log.debug("DataValidation 回写成功 - 字段: {}, 回写字段: {}, 值: {}",
                         field.getName(), writeBackField, writeBackValue);
            }

        } catch (Exception e) {
            log.warn("处理 DataValidation 字段失败 - 字段: {}, 错误: {}", field.getName(), e.getMessage());
        }
    }

    /**
     * 处理单个 DictDataValidation 注解字段
     *
     * @param obj        目标对象
     * @param field      字段
     * @param annotation 注解
     */
    private static void processDictDataValidationField(Object obj, Field field, DictDataValidation annotation) {
        try {
            field.setAccessible(true);
            Object fieldValue = field.get(obj);

            // 获取注解参数
            String writeBackField = annotation.writeBackField();
            boolean isMustEnable = annotation.isMustEnable();

            // 进行判定逻辑（这里可以根据具体业务需求扩展）
            boolean validationPassed = performDictDataValidationLogic(fieldValue, annotation);

            // 如果验证通过且有回写字段，则进行回写
            if (validationPassed && StrUtil.isNotBlank(writeBackField)) {
                Object writeBackValue = getDictDataValidationWriteBackValue(fieldValue, annotation);
                setFieldValue(obj, writeBackField, writeBackValue);
                log.debug("DictDataValidation 回写成功 - 字段: {}, 回写字段: {}, 值: {}",
                         field.getName(), writeBackField, writeBackValue);
            }

        } catch (Exception e) {
            log.warn("处理 DictDataValidation 字段失败 - 字段: {}, 错误: {}", field.getName(), e.getMessage());
        }
    }

    /**
     * 执行 DataValidation 判定逻辑
     * TODO: 根据具体业务需求实现验证逻辑
     *
     * @param fieldValue 字段值
     * @param annotation 注解
     * @return 是否验证通过
     */
    private static boolean performDataValidationLogic(Object fieldValue, DataValidation annotation) {
        // 示例逻辑：如果字段值不为空则验证通过
        // 实际使用时需要根据 annotation.dataType() 进行具体的业务验证
        return fieldValue != null && StrUtil.isNotBlank(fieldValue.toString());
    }

    /**
     * 执行 DictDataValidation 判定逻辑
     * TODO: 根据具体业务需求实现验证逻辑
     *
     * @param fieldValue 字段值
     * @param annotation 注解
     * @return 是否验证通过
     */
    private static boolean performDictDataValidationLogic(Object fieldValue, DictDataValidation annotation) {
        // 示例逻辑：如果字段值不为空则验证通过
        // 实际使用时需要根据 annotation.dictDataType() 查询字典数据进行验证
        return fieldValue != null && StrUtil.isNotBlank(fieldValue.toString());
    }

    /**
     * 获取 DataValidation 回写值
     * TODO: 根据具体业务需求实现回写值获取逻辑
     *
     * @param fieldValue 字段值
     * @param annotation 注解
     * @return 回写值
     */
    private static Object getDataValidationWriteBackValue(Object fieldValue, DataValidation annotation) {
        // 示例逻辑：返回字段值的字符串形式
        // 实际使用时需要根据 annotation.dataType() 查询相关数据
        return fieldValue != null ? fieldValue.toString() + "_processed" : null;
    }

    /**
     * 获取 DictDataValidation 回写值
     * TODO: 根据具体业务需求实现回写值获取逻辑
     *
     * @param fieldValue 字段值
     * @param annotation 注解
     * @return 回写值
     */
    private static Object getDictDataValidationWriteBackValue(Object fieldValue, DictDataValidation annotation) {
        // 示例逻辑：返回字段值的字符串形式
        // 实际使用时需要根据 annotation.dictDataType() 查询字典数据
        return fieldValue != null ? fieldValue.toString() + "_dict_processed" : null;
    }

    /**
     * 设置对象字段值
     *
     * @param obj       目标对象
     * @param fieldName 字段名
     * @param value     要设置的值
     * @return 是否设置成功
     */
    private static boolean setFieldValue(Object obj, String fieldName, Object value) {
        try {
            ReflectUtil.setFieldValue(obj, fieldName, value);
            return true;
        } catch (Exception e) {
            log.warn("设置字段值失败 - 对象: {}, 字段: {}, 值: {}, 错误: {}",
                    obj.getClass().getSimpleName(), fieldName, value, e.getMessage());
            return false;
        }
    }
}
