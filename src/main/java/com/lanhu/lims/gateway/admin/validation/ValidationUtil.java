package com.lanhu.lims.gateway.admin.validation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.lanhu.lims.gateway.admin.validation.annotation.DataValidation;
import com.lanhu.lims.gateway.admin.validation.annotation.DictDataValidation;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/********************************
 * @title ValidationUtil
 * @package com.lanhu.lims.gateway.admin.validation
 * @description 验证工具类，用于获取和处理 DataValidation 和 DictDataValidation 注解
 *
 * <AUTHOR>
 * @date 2025/6/19 16:40
 * @version 0.0.1
 *********************************/
@Slf4j
public class ValidationUtil {

    /**
     * 获取对象中所有带有 DataValidation 注解的字段信息
     *
     * @param obj 目标对象
     * @return DataValidation 注解字段信息列表
     */
    public static List<DataValidationInfo> getDataValidationFields(Object obj) {
        if (obj == null) {
            return new ArrayList<>();
        }
        List<DataValidationInfo> result = new ArrayList<>();
        Class<?> clazz = obj.getClass();
        Field[] fields = ReflectUtil.getFields(clazz);
        for (Field field : fields) {
            DataValidation annotation = field.getAnnotation(DataValidation.class);
            if (annotation != null) {
                try {
                    field.setAccessible(true);
                    Object fieldValue = field.get(obj);
                    result.add(new DataValidationInfo(field, annotation, fieldValue));
                } catch (IllegalAccessException e) {
                    log.warn("无法访问字段 {}: {}", field.getName(), e.getMessage());
                }
            }
        }
        return result;
    }

    /**
     * 获取对象中所有带有 DictDataValidation 注解的字段信息
     *
     * @param obj 目标对象
     * @return DictDataValidation 注解字段信息列表
     */
    public static List<DictDataValidationInfo> getDictDataValidationFields(Object obj) {
        if (obj == null) {
            return new ArrayList<>();
        }
        List<DictDataValidationInfo> result = new ArrayList<>();
        Class<?> clazz = obj.getClass();
        Field[] fields = ReflectUtil.getFields(clazz);
        for (Field field : fields) {
            DictDataValidation annotation = field.getAnnotation(DictDataValidation.class);
            if (annotation != null) {
                try {
                    field.setAccessible(true);
                    Object fieldValue = field.get(obj);
                    result.add(new DictDataValidationInfo(field, annotation, fieldValue));
                } catch (IllegalAccessException e) {
                    log.warn("无法访问字段 {}: {}", field.getName(), e.getMessage());
                }
            }
        }
        return result;
    }

    /**
     * 获取对象中所有验证注解字段信息（包括 DataValidation 和 DictDataValidation）
     *
     * @param obj 目标对象
     * @return 所有验证注解字段信息
     */
    public static AllValidationInfo getAllValidationFields(Object obj) {
        List<DataValidationInfo> dataValidationFields = getDataValidationFields(obj);
        List<DictDataValidationInfo> dictDataValidationFields = getDictDataValidationFields(obj);
        return new AllValidationInfo(dataValidationFields, dictDataValidationFields);
    }

    /**
     * 根据字段名设置对象字段值（用于回写功能）
     *
     * @param obj       目标对象
     * @param fieldName 字段名
     * @param value     要设置的值
     * @return 是否设置成功
     */
    public static boolean setFieldValue(Object obj, String fieldName, Object value) {
        if (obj == null || StrUtil.isBlank(fieldName)) {
            return false;
        }
        try {
            Field field = ReflectUtil.getField(obj.getClass(), fieldName);
            if (field != null) {
                ReflectUtil.setFieldValue(obj, field, value);
                return true;
            }
        } catch (Exception e) {
            log.warn("设置字段值失败 - 对象: {}, 字段: {}, 值: {}, 错误: {}",
                    obj.getClass().getSimpleName(), fieldName, value, e.getMessage());
        }
        return false;
    }

    /**
     * 检查字段值是否为空（用于必填验证）
     *
     * @param value 字段值
     * @return 是否为空
     */
    public static boolean isEmpty(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return StrUtil.isBlank((String) value);
        }
        if (value instanceof List) {
            return CollUtil.isEmpty((List<?>) value);
        }
        return false;
    }

    /**
     * DataValidation 注解字段信息
     */
    public static class DataValidationInfo {
        private final Field field;
        private final DataValidation annotation;
        private final Object fieldValue;

        public DataValidationInfo(Field field, DataValidation annotation, Object fieldValue) {
            this.field = field;
            this.annotation = annotation;
            this.fieldValue = fieldValue;
        }

        public Field getField() { return field; }
        public DataValidation getAnnotation() { return annotation; }
        public Object getFieldValue() { return fieldValue; }
        public String getFieldName() { return field.getName(); }
    }

    /**
     * DictDataValidation 注解字段信息
     */
    public static class DictDataValidationInfo {
        private final Field field;
        private final DictDataValidation annotation;
        private final Object fieldValue;

        public DictDataValidationInfo(Field field, DictDataValidation annotation, Object fieldValue) {
            this.field = field;
            this.annotation = annotation;
            this.fieldValue = fieldValue;
        }

        public Field getField() { return field; }
        public DictDataValidation getAnnotation() { return annotation; }
        public Object getFieldValue() { return fieldValue; }
        public String getFieldName() { return field.getName(); }
    }

    /**
     * 所有验证注解字段信息
     */
    public static class AllValidationInfo {
        private final List<DataValidationInfo> dataValidationFields;
        private final List<DictDataValidationInfo> dictDataValidationFields;

        public AllValidationInfo(List<DataValidationInfo> dataValidationFields,
                               List<DictDataValidationInfo> dictDataValidationFields) {
            this.dataValidationFields = dataValidationFields;
            this.dictDataValidationFields = dictDataValidationFields;
        }

        public List<DataValidationInfo> getDataValidationFields() { return dataValidationFields; }
        public List<DictDataValidationInfo> getDictDataValidationFields() { return dictDataValidationFields; }

        public boolean hasValidationFields() {
            return CollUtil.isNotEmpty(dataValidationFields) || CollUtil.isNotEmpty(dictDataValidationFields);
        }
    }
}
