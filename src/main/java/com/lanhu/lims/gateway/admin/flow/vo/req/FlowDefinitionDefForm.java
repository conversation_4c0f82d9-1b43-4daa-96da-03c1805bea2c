package com.lanhu.lims.gateway.admin.flow.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 查看流程定义图入参
 * @author: huangzheng
 * @date: 2025/6/19 14:42
 */
@Data
@ApiModel(value = "查看流程定义图入参")
public class FlowDefinitionDefForm {


    /**
     * 流程定义id
     */
    @ApiModelProperty(value = "流程定义id")
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    private Long id;



}
