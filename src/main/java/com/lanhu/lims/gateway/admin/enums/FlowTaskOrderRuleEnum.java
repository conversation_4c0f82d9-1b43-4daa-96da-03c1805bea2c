package com.lanhu.lims.gateway.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 流程任务排序规则枚举
 * @author: huangzheng
 * @date: 2025/6/18 13:49
 */
@Getter
@AllArgsConstructor
public enum FlowTaskOrderRuleEnum {

    /**
     * 排序规则 0:最新到达优先, 1: 最久等待优先 ,2: 创建时间由近到远 ,3: 创建时间由远到近
     */

    LATEST_ARRIVAL_FIRST(0, "最新到达优先"),

    LONGEST_WAITING_FIRST(1, "最久等待优先"),

    CREATE_TIME_NEAR_FAR(2, "创建时间由近到远"),

    CREATE_TIME_FAR_NEAR(3, "创建时间由远到近"),

    ;


    private final int code;

    private final String msg;

    public static FlowTaskOrderRuleEnum convert(int code) {
        for (FlowTaskOrderRuleEnum value : FlowTaskOrderRuleEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return FlowTaskOrderRuleEnum.LATEST_ARRIVAL_FIRST;
    }






}
