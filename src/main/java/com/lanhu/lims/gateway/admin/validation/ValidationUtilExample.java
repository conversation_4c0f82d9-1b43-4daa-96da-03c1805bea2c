package com.lanhu.lims.gateway.admin.validation;

import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.validation.annotation.DataValidation;
import com.lanhu.lims.gateway.admin.validation.annotation.DictDataValidation;
import com.lanhu.lims.gateway.admin.validation.enums.DataTypeEnum;
import lombok.Data;

import java.util.List;

/********************************
 * @title ValidationUtilExample
 * @package com.lanhu.lims.gateway.admin.validation
 * @description ValidationUtil 使用示例
 *
 * <AUTHOR>
 * @date 2025/6/19 17:00
 * @version 0.0.1
 *********************************/
public class ValidationUtilExample {

    /**
     * 示例实体类
     */
    @Data
    public static class ExampleEntity {
        
        @DataValidation(dataType = DataTypeEnum.DETECTION_METHOD, writeBackField = "detectionMethodName", mustEnabled = true)
        private String detectionMethodId;
        
        private String detectionMethodName; // 回写字段
        
        @DictDataValidation(dictDataType = DictDataTypeEnum.EQUIPMENT_USAGE, writeBackField = "usageList", isMustEnable = true)
        private String usage;
        
        private List<Object> usageList; // 回写字段
        
        @DictDataValidation(dictDataType = DictDataTypeEnum.EQUIPMENT_STATUS, isMustEnable = false)
        private String status;
        
        // 没有注解的字段
        private String normalField;
    }

    /**
     * 使用示例
     */
    public static void main(String[] args) {
        ExampleEntity entity = new ExampleEntity();
        entity.setDetectionMethodId("123");
        entity.setUsage("usage1,usage2");
        entity.setStatus("1");
        entity.setNormalField("normal");

        System.out.println("=== 处理前 ===");
        System.out.println("detectionMethodName: " + entity.getDetectionMethodName());
        System.out.println("usageList: " + entity.getUsageList());

        // 处理所有验证注解字段（核心功能）
        ValidationUtil.processAllValidation(entity);

        System.out.println("=== 处理后 ===");
        System.out.println("detectionMethodName: " + entity.getDetectionMethodName());
        System.out.println("usageList: " + entity.getUsageList());

        // 也可以单独处理某种类型的注解
        System.out.println("\n=== 单独处理示例 ===");

        ExampleEntity entity2 = new ExampleEntity();
        entity2.setDetectionMethodId("456");
        entity2.setUsage("usage3");

        // 只处理 DataValidation 注解
        ValidationUtil.processDataValidation(entity2);
        System.out.println("只处理 DataValidation 后 - detectionMethodName: " + entity2.getDetectionMethodName());

        // 只处理 DictDataValidation 注解
        ValidationUtil.processDictDataValidation(entity2);
        System.out.println("只处理 DictDataValidation 后 - usageList: " + entity2.getUsageList());
    }
}
