
package com.lanhu.lims.gateway.admin.enums;
import lombok.AllArgsConstructor;
import lombok.Getter;
/**
 * 审批大类枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/9 17:11
 */
@Getter
@AllArgsConstructor
public enum FlowBusinessCategoryEnum {

    /**
     * 静态数据业务，委托单业务，样本业务，检测业务，报告业务，物资业务，资源业务，文件业务等...
     */

    STATIC_DATA_INSERT_BUSINESS(1000, "静态数据新增审批"),
    STATIC_DATA_EDIT_BUSINESS(1001, "静态数据编辑审批"),
    STATIC_DATA_DEL_BUSINESS(1002, "静态数据删除审批"),
    STATIC_DATA_VERSION_BUSINESS(1003, "静态数据新增版本审批"),
    ENTRUST_ORDER_INSERT_BUSINESS(1010, "委托单新增审批"),
    ENTRUST_ORDER_EDIT_BUSINESS(1011, "委托单编辑审批"),
    ENTRUST_ORDER_TERMINATION_BUSINESS(1012, "委托单终止审批"),
    SAMPLE_DESTROY_BUSINESS(1020, "样本销毁审批"),
    DETECTION_RESULT_BUSINESS(1030, "检测结果审批"),
    REPORT_GENERATE_BUSINESS(1040, "报告生成审批"),
    CONSUMABLE_IN_BUSINESS(1050, "耗材入库审批"),
    CONSUMABLE_OUT_BUSINESS(1051, "耗材出库审批"),
    REAGENT_IN_BUSINESS(1060, "试剂入库审批"),
    REAGENT_OUT_BUSINESS(1061, "试剂出库审批"),
    CUSTOMER_INSERT_BUSINESS(1070, "客户新增审批"),
    CUSTOMER_EDIT_BUSINESS(1071, "客户编辑审批"),
    CUSTOMER_DEL_BUSINESS(1072, "客户删除审批"),
    CUSTOMER_HMD_BUSINESS(1073, "客户黑名单审批"),
    OUTSOURCER_INSERT_BUSINESS(1080, "外包商新增审批"),
    OUTSOURCER_EDIT_BUSINESS(1081, "外包商编辑审批"),
    OUTSOURCER_DEL_BUSINESS(1082, "外包商删除审批"),
    OUTSOURCER_HMD_BUSINESS(1083, "外包商黑名单审批"),
    SYSTEM_FILE_INSERT_BUSINESS(1090, "系统文件新增审批"),
    SYSTEM_FILE_EDIT_BUSINESS(1091, "系统文件编辑审批"),
    SYSTEM_FILE_DEL_BUSINESS(1092, "系统文件删除审批"),
    TEMPLATE_FILE_INSERT_BUSINESS(1100, "模板文件新增审批"),
    TEMPLATE_FILE_EDIT_BUSINESS(1101, "模板文件编辑审批"),
    TEMPLATE_FILE_DEL_BUSINESS(1102, "模板文件删除审批"),


    ;




    private final int code;

    private final String msg;

    public static FlowBusinessCategoryEnum convert(int code) {
        for (FlowBusinessCategoryEnum value : FlowBusinessCategoryEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return FlowBusinessCategoryEnum.STATIC_DATA_INSERT_BUSINESS;
    }
}