package com.lanhu.lims.gateway.admin.flow.controller;

import com.github.pagehelper.PageInfo;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.flow.service.FlowInstanceService;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowInstanceActiveForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowInstanceApproveRecordForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowInstanceChartForm;
import com.lanhu.lims.gateway.admin.flow.vo.req.FlowInstanceUnActiveForm;
import com.lanhu.lims.gateway.admin.flow.vo.resp.FlowInstanceRecordVo;
import com.lanhu.lims.gateway.admin.flow.vo.resp.InstanceListVO;
import com.lanhu.lims.gateway.admin.vo.req.FlowInstanceListForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 工作流实例相关接口
 * @author: huangzheng
 * @date: 2025/5/29 14:47
 */

@RestController
@Api(tags = "工作流-实例相关接口",value = "工作流-实例相关接口")
public class FlowInstanceController extends BaseController {


    @Resource
    private FlowInstanceService flowInstanceService;




    /**
     * 实例审批记录查询
     */
    @PostMapping("/flow/task/v1/record")
    @ApiOperation("工作流实例 实例审批记录查询")
    public PcsResult<List<FlowInstanceRecordVo>> record(@Validated @RequestBody FlowInstanceApproveRecordForm flowInstanceApproveRecordForm) {

        return flowInstanceService.getFlowInstanceRecord(flowInstanceApproveRecordForm);

    }



    /**
     * 查询当前流程实例流程图
     *
     */
    @PostMapping("/flow/instance/v1/chart")
    @ApiOperation("工作流实例 当前流程实例流程图")
    public PcsResult<String> flowChart(@Validated @RequestBody FlowInstanceChartForm flowInstanceChartForm) {

        return flowInstanceService.getFlowInstanceChart(flowInstanceChartForm);

    }


    /**
     * 激活流程
     */
    @PostMapping("/flow/instance/v1/active")
    @ApiOperation("工作流实例 激活流程实例")
    public PcsResult<Boolean> active(@Validated @RequestBody FlowInstanceActiveForm flowInstanceActiveForm) {

        return flowInstanceService.active(flowInstanceActiveForm);

    }



    /**
     * 挂起流程
     */
    @ApiOperation("工作流实例 挂起流程实例")
    @PostMapping("/flow/instance/v1/unActive")
    public PcsResult<Boolean> unActive(@Validated @RequestBody FlowInstanceUnActiveForm flowInstanceUnActiveForm) {

        return flowInstanceService.unActive(flowInstanceUnActiveForm);

    }




    /**
     * 查询所有的实例，需要有实例查询权限
     */
//    @RequiresPermissions("flow:instance:list")
    @ApiOperation("工作流实例 查询所有实例列表")
    @PostMapping("/flow/instance/v1/list")
    public PcsResult<PageInfo<InstanceListVO>> list(@Validated @RequestBody FlowInstanceListForm flowInstanceListForm) {

        return flowInstanceService.list(flowInstanceListForm);

    }
















}
