package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;import java.util.Date;import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 部门表
 */
@ApiModel(value = "部门表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_dept")
public class Dept {
    /**
     * 部门id
     */
    @TableId(value = "dept_id", type = IdType.AUTO)
    @ApiModelProperty(value = "部门id")
    private Long deptId;

    /**
     * 父部门id
     */
    @TableField(value = "parent_id")
    @ApiModelProperty(value = "父部门id")
    private Long parentId;

    /**
     * 祖级列表
     */
    @TableField(value = "ancestors")
    @ApiModelProperty(value = "祖级列表")
    private String ancestors;

    /**
     * 部门名称
     */
    @TableField(value = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 显示顺序
     */
    @TableField(value = "order_num")
    @ApiModelProperty(value = "显示顺序")
    private Integer orderNum;

    /**
     * 负责人
     */
    @TableField(value = "leader")
    @ApiModelProperty(value = "负责人")
    private String leader;

    /**
     * 联系电话
     */
    @TableField(value = "phone")
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 部门状态（0停用 1启用）
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "部门状态（0停用 1启用）")
    private Integer status;

    /**
     * 删除 0:正常 , 1:删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "删除 0:正常 , 1:删除")
    private Integer isEffect;

    /**
     * 创建者
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建者")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 组织类型，0：公司，1：部门，2：小组
     */
    @TableField(value = "`type`")
    @ApiModelProperty(value = "组织类型，0：公司，1：部门，2：小组")
    private Integer type;



    @TableField(exist = false)
    @ApiModelProperty(value = "是否被选中")
    private boolean checked = false;

    @TableField(exist = false)
    @ApiModelProperty(value = "子部门列表")
    private List<com.lanhu.lims.gateway.admin.model.Dept> children = new ArrayList<>();


    @TableField(exist = false)
    @ApiModelProperty(value = "上级部门名称")
    private String parentDeptName;
}